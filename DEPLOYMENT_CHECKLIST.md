# MCP Qdrant工具部署检查清单

## ✅ 部署前检查

### 1. 系统环境
- [ ] Python 3.8+ 已安装
- [ ] Ollama 已安装并运行
- [ ] 足够的磁盘空间 (至少2GB)
- [ ] 网络连接正常 (用于下载模型)

### 2. 目录权限
- [ ] `E:\Desktop\obsidian` 目录可读写
- [ ] `D:\work\project1\qdrant` 目录可读写
- [ ] 项目目录 `d:\work\project2` 可读写

### 3. Ollama配置
- [ ] Ollama服务运行中 (`ollama serve`)
- [ ] 可以访问 `http://localhost:11434`
- [ ] 嵌入模型已下载 (`ollama pull mxbai-embed-large:latest`)

## 🚀 部署步骤

### 步骤1: 安装依赖
```bash
cd d:\work\project2
python install.py
```

**检查点:**
- [ ] 所有Python包安装成功
- [ ] 目录创建成功
- [ ] Ollama连接正常
- [ ] 嵌入模型下载完成

### 步骤2: 系统测试
```bash
python test_system.py
```

**检查点:**
- [ ] 嵌入服务测试通过
- [ ] 向量存储测试通过
- [ ] 文档处理器测试通过
- [ ] 集成测试通过

### 步骤3: 启动服务
```bash
python start_server.py
# 或
start.bat
```

**检查点:**
- [ ] 服务器启动无错误
- [ ] 文件监控启动成功
- [ ] 向量数据库初始化完成
- [ ] MCP服务器监听正常

## 🔧 Claude Desktop集成

### 配置文件位置
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### 配置内容
```json
{
  "mcpServers": {
    "qdrant-document-server": {
      "command": "python",
      "args": ["d:\\work\\project2\\start_server.py"],
      "env": {
        "PYTHONPATH": "d:\\work\\project2"
      }
    }
  }
}
```

**检查点:**
- [ ] 配置文件路径正确
- [ ] 项目路径使用双反斜杠
- [ ] Claude Desktop重启后识别服务器

## 🧪 功能验证

### 1. 文档自动处理
1. [ ] 在 `E:\Desktop\obsidian` 中放入测试文档
2. [ ] 观察日志，确认文档被处理
3. [ ] 检查向量数据库中是否有新记录

### 2. MCP工具测试
在Claude Desktop中测试以下工具:

#### search_documents
```
请搜索关于"人工智能"的文档
```
- [ ] 工具调用成功
- [ ] 返回相关文档
- [ ] 相似度分数合理

#### list_documents
```
请列出所有文档
```
- [ ] 显示文档列表
- [ ] 信息完整准确

#### get_collection_info
```
请显示向量数据库信息
```
- [ ] 显示集合统计
- [ ] 数据准确

### 3. 文件监控测试
1. [ ] 添加新文档 → 自动处理
2. [ ] 修改现有文档 → 自动更新
3. [ ] 删除文档 → 自动从数据库移除
4. [ ] 重命名文档 → 正确处理

## 🐛 故障排除

### 常见错误及解决方案

#### 1. "Ollama连接失败"
**解决方案:**
- [ ] 检查Ollama是否运行: `ollama serve`
- [ ] 检查端口是否被占用: `netstat -an | findstr 11434`
- [ ] 重启Ollama服务

#### 2. "模型不可用"
**解决方案:**
- [ ] 手动拉取模型: `ollama pull mxbai-embed-large:latest`
- [ ] 检查网络连接
- [ ] 检查磁盘空间

#### 3. "文档处理失败"
**解决方案:**
- [ ] 检查文件格式是否支持
- [ ] 检查文件编码 (UTF-8/GBK)
- [ ] 检查文件是否损坏
- [ ] 检查文件权限

#### 4. "向量数据库错误"
**解决方案:**
- [ ] 检查Qdrant目录权限
- [ ] 检查磁盘空间
- [ ] 删除损坏的数据库文件重新初始化

#### 5. "MCP工具不可用"
**解决方案:**
- [ ] 检查Claude Desktop配置
- [ ] 重启Claude Desktop
- [ ] 检查服务器日志
- [ ] 验证项目路径

## 📊 性能监控

### 关键指标
- [ ] 文档处理速度 (文档/分钟)
- [ ] 向量搜索响应时间 (<1秒)
- [ ] 内存使用情况 (<2GB)
- [ ] 磁盘使用情况

### 日志监控
- [ ] 服务器启动日志正常
- [ ] 文档处理日志无错误
- [ ] MCP工具调用日志正常
- [ ] 错误日志及时处理

## 🔄 维护任务

### 日常维护
- [ ] 检查日志文件大小
- [ ] 监控磁盘空间使用
- [ ] 检查服务运行状态
- [ ] 备份重要文档

### 定期维护
- [ ] 更新Python依赖包
- [ ] 更新Ollama版本
- [ ] 清理过期日志文件
- [ ] 优化向量数据库

## 📈 扩展计划

### 短期扩展
- [ ] 添加更多文档格式支持
- [ ] 优化文档分块策略
- [ ] 实现批量导入功能
- [ ] 添加Web管理界面

### 长期扩展
- [ ] 支持多语言文档
- [ ] 实现分布式部署
- [ ] 集成其他向量数据库
- [ ] 添加高级搜索功能

## ✅ 部署完成确认

当以下所有项目都完成时，部署即为成功:

- [ ] 所有系统测试通过
- [ ] MCP服务器正常运行
- [ ] Claude Desktop集成成功
- [ ] 文档自动处理正常
- [ ] 所有MCP工具可用
- [ ] 搜索功能准确有效

**部署完成时间:** _______________
**部署人员:** _______________
**验证人员:** _______________

---

🎉 **恭喜！MCP Qdrant文档向量化工具部署完成！**
