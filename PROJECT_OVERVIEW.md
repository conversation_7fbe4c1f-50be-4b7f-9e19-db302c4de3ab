# MCP Qdrant文档向量化工具 - 项目概览

## 🎯 项目目标

创建一个基于Model Context Protocol (MCP)的工具，实现：
- 自动监控本地文档文件夹 `E:\Desktop\obsidian`
- 使用Ollama本地嵌入模型 `mxbai-embed-large:latest` 进行文档向量化
- 将向量存储到Qdrant本地数据库 `D:\work\project1\qdrant`
- 提供MCP接口供大模型调用，实现智能文档检索

## 📁 项目结构

```
mcp-qdrant-tool/
├── 📄 核心模块
│   ├── mcp_server.py          # MCP服务器主文件 (JSON-RPC实现)
│   ├── document_processor.py  # 文档处理模块 (支持多种格式)
│   ├── embedding_service.py   # Ollama嵌入服务
│   ├── vector_store.py        # Qdrant向量数据库操作
│   └── file_monitor.py        # 文件监控服务 (实时同步)
│
├── ⚙️ 配置与工具
│   ├── config.py             # 配置文件
│   ├── requirements.txt      # Python依赖
│   ├── install.py            # 自动安装脚本
│   ├── test_system.py        # 系统测试脚本
│   └── start_server.py       # 服务器启动脚本
│
├── 🚀 启动工具
│   ├── start.bat             # Windows批处理启动脚本
│   └── claude_desktop_config.json  # Claude Desktop配置示例
│
└── 📚 文档
    ├── README.md             # 详细使用说明
    └── PROJECT_OVERVIEW.md   # 项目概览 (本文件)
```

## 🔧 核心功能

### 1. 文档处理 (`document_processor.py`)
- **支持格式**: TXT, MD, PDF, DOCX, HTML, JSON
- **编码处理**: 自动检测UTF-8/GBK编码
- **内容提取**: 纯文本提取，去除格式标记
- **元数据**: 文件大小、修改时间、创建时间

### 2. 嵌入服务 (`embedding_service.py`)
- **模型**: mxbai-embed-large:latest (1024维向量)
- **自动拉取**: 检测模型可用性，自动下载
- **批量处理**: 支持批量文本嵌入
- **错误处理**: 连接失败重试机制

### 3. 向量存储 (`vector_store.py`)
- **数据库**: Qdrant本地文件存储
- **相似度**: 余弦相似度计算
- **CRUD操作**: 增删改查文档向量
- **元数据存储**: 完整文档信息保存

### 4. 文件监控 (`file_monitor.py`)
- **实时监控**: 使用watchdog监控文件变化
- **事件处理**: 新增、修改、删除、移动
- **初始扫描**: 启动时扫描现有文件
- **去重处理**: 避免重复处理同一文件

### 5. MCP服务器 (`mcp_server.py`)
- **协议**: JSON-RPC 2.0实现
- **工具接口**: 5个核心工具
- **错误处理**: 完善的异常处理机制
- **日志记录**: 详细的操作日志

## 🛠️ MCP工具接口

| 工具名称 | 功能描述 | 参数 |
|---------|---------|------|
| `search_documents` | 搜索相关文档 | `query`: 搜索文本<br>`limit`: 结果数量(可选) |
| `add_document` | 手动添加文档 | `file_path`: 文档路径 |
| `delete_document` | 删除文档 | `file_path`: 文档路径 |
| `list_documents` | 列出所有文档 | 无参数 |
| `get_collection_info` | 获取数据库信息 | 无参数 |

## 🚀 快速开始

### 1. 自动安装
```bash
python install.py
```

### 2. 系统测试
```bash
python test_system.py
```

### 3. 启动服务器
```bash
# 方式1: Python脚本
python start_server.py

# 方式2: Windows批处理
start.bat
```

### 4. Claude Desktop集成
将 `claude_desktop_config.json` 内容添加到Claude Desktop配置中。

## 📋 系统要求

### 必需组件
- **Python**: 3.8+ 
- **Ollama**: 最新版本
- **磁盘空间**: 至少2GB (模型+数据库)

### Python依赖
- `qdrant-client`: 向量数据库客户端
- `ollama`: Ollama Python SDK
- `watchdog`: 文件监控
- `PyPDF2`: PDF处理
- `python-docx`: Word文档处理
- `beautifulsoup4`: HTML处理
- `markdown`: Markdown处理

## 🔄 工作流程

```mermaid
graph TD
    A[文档放入监控文件夹] --> B[文件监控检测变化]
    B --> C[文档处理器提取内容]
    C --> D[Ollama生成嵌入向量]
    D --> E[Qdrant存储向量]
    E --> F[MCP工具可用]
    F --> G[大模型调用搜索]
    G --> H[返回相关文档]
```

## 🧪 测试验证

### 自动测试
- **嵌入服务测试**: 验证Ollama连接和模型可用性
- **向量存储测试**: 验证Qdrant数据库操作
- **文档处理测试**: 验证各种格式文档处理
- **集成测试**: 验证完整工作流程

### 手动测试
1. 将测试文档放入 `E:\Desktop\obsidian`
2. 观察日志输出，确认文档被处理
3. 使用MCP工具搜索文档
4. 验证搜索结果准确性

## 🔧 配置说明

### 路径配置 (`config.py`)
```python
DOCUMENT_FOLDER = r"E:\Desktop\obsidian"    # 文档监控路径
QDRANT_PATH = r"D:\work\project1\qdrant"    # 向量数据库路径
```

### Ollama配置
```python
OLLAMA_HOST = "http://localhost:11434"       # Ollama服务地址
EMBEDDING_MODEL = "mxbai-embed-large:latest" # 嵌入模型
```

### 向量配置
```python
VECTOR_DIMENSION = 1024                      # 向量维度
QDRANT_COLLECTION_NAME = "documents"         # 集合名称
```

## 🐛 故障排除

### 常见问题
1. **Ollama连接失败**: 确保Ollama服务运行 (`ollama serve`)
2. **模型不可用**: 手动拉取模型 (`ollama pull mxbai-embed-large:latest`)
3. **文档处理失败**: 检查文件格式和编码
4. **向量存储错误**: 检查磁盘空间和权限

### 日志查看
- 控制台输出: 实时日志
- 文件日志: `mcp_server.log`

## 🔮 扩展可能

### 功能扩展
- 支持更多文档格式 (PPT, Excel等)
- 添加文档分块策略
- 实现增量更新机制
- 支持多语言文档

### 性能优化
- 并发文档处理
- 向量索引优化
- 缓存机制
- 批量操作优化

### 集成扩展
- Web界面
- REST API
- 其他向量数据库支持
- 云端部署支持

## 📄 许可证

MIT License - 详见项目根目录LICENSE文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！
