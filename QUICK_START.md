# 快速启动指南

## 🚀 当前问题解决方案

根据你的截图，Ollama服务连接有问题。以下是解决步骤：

### 步骤1: 启动Ollama服务

**方法1 - 使用提供的脚本:**
```bash
start_ollama.bat
```

**方法2 - 手动启动:**
```bash
D:\ollama\ollama.exe serve
```

**重要:** 保持Ollama服务窗口打开，不要关闭！

### 步骤2: 验证服务运行

在新的命令行窗口中运行：
```bash
D:\ollama\ollama.exe list
```

应该能看到你的模型列表，包括 `mxbai-embed-large:latest`

### 步骤3: 修复连接问题

运行修复脚本：
```bash
python fix_ollama_connection.py
```

这个脚本会：
- 检查Ollama进程是否运行
- 检查服务是否响应
- 验证嵌入模型可用性
- 测试嵌入功能

### 步骤4: 重新运行系统测试

```bash
python test_system.py
```

### 步骤5: 启动MCP服务器

```bash
python start_server.py
```

## 🔧 常见问题解决

### 问题1: "HTTP/1.1 502 Bad Gateway"
**原因:** Ollama服务未运行
**解决:** 运行 `start_ollama.bat` 或 `D:\ollama\ollama.exe serve`

### 问题2: "embedding_service.py检查模型可用性失败"
**原因:** 无法连接到Ollama API
**解决:** 
1. 确保Ollama服务运行在端口11434
2. 检查防火墙设置
3. 运行 `fix_ollama_connection.py`

### 问题3: 模型不可用
**原因:** 嵌入模型未正确安装
**解决:** 
```bash
D:\ollama\ollama.exe pull mxbai-embed-large:latest
```

## 📋 完整启动流程

1. **启动Ollama服务** (保持运行)
   ```bash
   start_ollama.bat
   ```

2. **新开命令行窗口，安装依赖**
   ```bash
   python install.py
   ```

3. **修复连接问题**
   ```bash
   python fix_ollama_connection.py
   ```

4. **系统测试**
   ```bash
   python test_system.py
   ```

5. **启动MCP服务器**
   ```bash
   python start_server.py
   ```

## 🎯 验证成功标志

当看到以下信息时，说明系统正常：

```
INFO:__main__:启动MCP服务器: qdrant-document-server v1.0.0
INFO:__main__:初始化向量数据库...
INFO:__main__:检查嵌入模型...
INFO:__main__:启动文件监控...
INFO:__main__:启动MCP服务器...
```

## 📁 文件夹准备

确保以下文件夹存在并可写：
- `E:\Desktop\obsidian` - 文档监控文件夹
- `D:\work\project1\qdrant` - 向量数据库文件夹

## 🧪 测试文档

在 `E:\Desktop\obsidian` 中放入测试文档：

**test.txt:**
```
这是一个测试文档。
包含一些关于人工智能的内容。
用于验证文档处理和向量化功能。
```

**test.md:**
```markdown
# 测试Markdown文档

这是一个**Markdown**格式的测试文档。

## 功能测试
- 文档处理
- 向量化
- 搜索功能
```

## 🔍 Claude Desktop集成

1. 找到Claude Desktop配置文件：
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. 添加配置：
```json
{
  "mcpServers": {
    "qdrant-document-server": {
      "command": "python",
      "args": ["d:\\work\\project2\\start_server.py"],
      "env": {
        "PYTHONPATH": "d:\\work\\project2"
      }
    }
  }
}
```

3. 重启Claude Desktop

4. 测试MCP工具：
   - "请搜索关于人工智能的文档"
   - "请列出所有文档"
   - "请显示向量数据库信息"

## 📞 需要帮助？

如果仍有问题，请提供：
1. `fix_ollama_connection.py` 的输出
2. `test_system.py` 的输出
3. 具体的错误信息

记住：**Ollama服务必须保持运行状态！**
