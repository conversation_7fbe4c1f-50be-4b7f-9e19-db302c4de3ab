# 快速启动指南

## 🚀 Python启动方式（推荐）

所有.bat文件已删除，现在使用Python脚本启动：

### 方法1: 自动启动（最简单）

```bash
python auto_start.py
```

这个脚本会自动：
- 检查Python环境
- 安装依赖包
- 检查Ollama服务
- 运行系统测试
- 启动MCP服务器

### 方法2: 快速启动

```bash
python quick_start.py
```

适合已经配置好环境的情况。

### 方法3: 分步启动

**步骤1: 启动Ollama服务**
```bash
python start_ollama.py
```

**步骤2: 启动MCP服务器**
```bash
python start_server.py
```

## 🔧 常见问题解决

### 问题1: 端口11434占用
**原因:** Ollama服务已运行或端口被占用
**解决:**
```bash
python start_ollama.py
```
脚本会自动检测和处理端口占用问题

### 问题2: Python模块缺失
**原因:** 依赖包未安装
**解决:**
```bash
python -m pip install -r requirements.txt
```

### 问题3: Ollama连接失败
**原因:** Ollama服务未运行
**解决:**
```bash
python start_ollama.py
```

### 问题4: 模型不可用
**原因:** 嵌入模型未正确安装
**解决:**
```bash
D:\ollama\ollama.exe pull mxbai-embed-large:latest
```

## 📋 推荐启动流程

### 最简单方式：
```bash
python auto_start.py
```

### 手动方式：
1. **启动Ollama服务**
   ```bash
   python start_ollama.py
   ```

2. **启动MCP服务器**
   ```bash
   python start_server.py
   ```

## 🎯 验证成功标志

当看到以下信息时，说明系统正常：

```
INFO:__main__:启动MCP服务器: qdrant-document-server v1.0.0
INFO:__main__:初始化向量数据库...
INFO:__main__:检查嵌入模型...
INFO:__main__:启动文件监控...
INFO:__main__:启动MCP服务器...
```

## 📁 文件夹准备

确保以下文件夹存在并可写：
- `E:\Desktop\obsidian` - 文档监控文件夹
- `D:\work\project1\qdrant` - 向量数据库文件夹

## 🧪 测试文档

在 `E:\Desktop\obsidian` 中放入测试文档：

**test.txt:**
```
这是一个测试文档。
包含一些关于人工智能的内容。
用于验证文档处理和向量化功能。
```

**test.md:**
```markdown
# 测试Markdown文档

这是一个**Markdown**格式的测试文档。

## 功能测试
- 文档处理
- 向量化
- 搜索功能
```

## 🔍 Claude Desktop集成

1. 找到Claude Desktop配置文件：
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. 添加配置：
```json
{
  "mcpServers": {
    "qdrant-document-server": {
      "command": "python",
      "args": ["d:\\work\\project2\\start_server.py"],
      "env": {
        "PYTHONPATH": "d:\\work\\project2"
      }
    }
  }
}
```

3. 重启Claude Desktop

4. 测试MCP工具：
   - "请搜索关于人工智能的文档"
   - "请列出所有文档"
   - "请显示向量数据库信息"

## 📞 需要帮助？

如果仍有问题，请提供：
1. `fix_ollama_connection.py` 的输出
2. `test_system.py` 的输出
3. 具体的错误信息

记住：**Ollama服务必须保持运行状态！**
