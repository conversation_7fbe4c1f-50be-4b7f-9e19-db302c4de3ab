# MCP Qdrant文档向量化工具

这是一个基于Model Context Protocol (MCP)的工具，用于将本地文档自动向量化并存储到Qdrant向量数据库中，支持大模型进行文档检索和问答。

## 功能特性

- 🔍 **自动文档监控**: 监控指定文件夹，自动处理新增、修改、删除的文档
- 📄 **多格式支持**: 支持TXT、MD、PDF、DOCX、HTML、JSON等多种文档格式
- 🤖 **本地嵌入**: 使用Ollama本地嵌入模型(mxbai-embed-large)进行向量化
- 💾 **向量存储**: 使用Qdrant本地向量数据库存储文档向量
- 🔧 **MCP接口**: 提供标准MCP工具接口，供大模型调用
- ⚡ **实时同步**: 文件变化实时同步到向量数据库

## 系统要求

- Python 3.8+
- Ollama (已安装mxbai-embed-large:latest模型)
- 足够的磁盘空间用于向量数据库存储

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd mcp-qdrant-tool
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置Ollama
确保Ollama已安装并运行，然后拉取嵌入模型：
```bash
ollama pull mxbai-embed-large:latest
```

### 4. 创建必要目录
```bash
# 创建文档监控目录
mkdir -p "E:\Desktop\obsidian"

# 创建Qdrant数据库目录
mkdir -p "D:\work\project1\qdrant"
```

## 配置说明

编辑 `config.py` 文件中的路径配置：

```python
# 文档监控路径
DOCUMENT_FOLDER = r"E:\Desktop\obsidian"

# Qdrant数据库路径
QDRANT_PATH = r"D:\work\project1\qdrant"

# Ollama配置
OLLAMA_HOST = "http://localhost:11434"
EMBEDDING_MODEL = "mxbai-embed-large:latest"
```

## 使用方法

### 启动MCP服务器
```bash
python mcp_server.py
```

### 可用的MCP工具

1. **search_documents**: 在向量数据库中搜索相关文档
   - 参数: `query` (搜索文本), `limit` (结果数量，可选)

2. **add_document**: 手动添加文档到向量数据库
   - 参数: `file_path` (文档路径)

3. **delete_document**: 从向量数据库中删除文档
   - 参数: `file_path` (文档路径)

4. **list_documents**: 列出向量数据库中的所有文档

5. **get_collection_info**: 获取向量数据库集合信息

### 文档管理

1. **自动监控**: 将文档放入 `E:\Desktop\obsidian` 文件夹，系统会自动处理
2. **支持格式**: .txt, .md, .pdf, .docx, .doc, .html, .htm, .json
3. **实时更新**: 文档修改后会自动更新向量数据库

## 项目结构

```
mcp-qdrant-tool/
├── mcp_server.py          # MCP服务器主文件
├── document_processor.py  # 文档处理模块
├── embedding_service.py   # Ollama嵌入服务
├── vector_store.py        # Qdrant向量数据库操作
├── file_monitor.py        # 文件监控服务
├── config.py             # 配置文件
├── requirements.txt      # 依赖包列表
└── README.md            # 项目说明
```

## 测试

### 测试各个模块
```bash
# 测试嵌入服务
python embedding_service.py

# 测试文档处理
python document_processor.py

# 测试向量存储
python vector_store.py

# 测试文件监控
python file_monitor.py
```

### 测试MCP工具
启动MCP服务器后，可以通过支持MCP的客户端（如Claude Desktop）调用工具进行测试。

## 故障排除

### 常见问题

1. **Ollama连接失败**
   - 确保Ollama服务正在运行
   - 检查OLLAMA_HOST配置是否正确

2. **嵌入模型不可用**
   - 运行 `ollama pull mxbai-embed-large:latest` 拉取模型
   - 检查模型名称是否正确

3. **文档处理失败**
   - 检查文档格式是否支持
   - 确保文档文件没有损坏

4. **向量数据库错误**
   - 检查Qdrant数据库路径是否可写
   - 确保有足够的磁盘空间

### 日志查看
程序运行时会输出详细日志，可以根据日志信息排查问题。

## 开发说明

### 添加新的文档格式支持
在 `document_processor.py` 中添加新的处理器：

```python
async def _process_new_format(self, file_path: str) -> Optional[str]:
    # 实现新格式的处理逻辑
    pass
```

### 自定义嵌入模型
修改 `config.py` 中的 `EMBEDDING_MODEL` 配置，使用其他Ollama支持的嵌入模型。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
