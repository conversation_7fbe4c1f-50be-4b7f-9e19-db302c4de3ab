#!/usr/bin/env python3
"""
自动启动MCP Qdrant工具的完整脚本
"""
import os
import sys
import subprocess
import time
import logging
import importlib.util

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_python():
    """检查Python环境"""
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"Python路径: {sys.executable}")
    return True

def install_dependencies():
    """安装依赖包"""
    logger.info("检查并安装依赖包...")
    
    try:
        # 检查requirements.txt是否存在
        if not os.path.exists("requirements.txt"):
            logger.error("requirements.txt文件不存在")
            return False
        
        # 安装依赖
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ 依赖包安装/更新完成")
            return True
        else:
            logger.error(f"依赖包安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"安装依赖包时出错: {e}")
        return False

def check_required_modules():
    """检查必需的模块"""
    required_modules = [
        'requests', 'ollama', 'qdrant_client', 'watchdog', 
        'PyPDF2', 'docx', 'markdown', 'bs4'
    ]
    
    missing_modules = []
    for module in required_modules:
        if importlib.util.find_spec(module) is None:
            missing_modules.append(module)
    
    if missing_modules:
        logger.warning(f"缺少模块: {missing_modules}")
        return False
    else:
        logger.info("✓ 所有必需模块已安装")
        return True

def run_ollama_check():
    """运行Ollama检查和启动"""
    try:
        logger.info("检查Ollama服务...")
        
        # 导入并运行start_ollama模块
        if os.path.exists("start_ollama.py"):
            import start_ollama
            result = start_ollama.main()
            return result == 0
        else:
            logger.error("start_ollama.py文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"Ollama检查失败: {e}")
        return False

def run_system_test():
    """运行系统测试"""
    try:
        logger.info("运行系统测试...")
        
        if not os.path.exists("test_system.py"):
            logger.warning("test_system.py文件不存在，跳过测试")
            return True
        
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✓ 系统测试通过")
            return True
        else:
            logger.warning("⚠ 系统测试未完全通过，但可以继续")
            logger.info("测试输出:")
            print(result.stdout)
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return True  # 即使测试失败也继续
            
    except Exception as e:
        logger.error(f"系统测试失败: {e}")
        return True  # 测试失败不阻止启动

def start_mcp_server():
    """启动MCP服务器"""
    try:
        logger.info("启动MCP服务器...")
        logger.info("按Ctrl+C停止服务器")
        
        if not os.path.exists("start_server.py"):
            logger.error("start_server.py文件不存在")
            return False
        
        # 直接运行，不捕获输出，让用户看到实时日志
        result = subprocess.run([sys.executable, "start_server.py"])
        return result.returncode == 0
        
    except KeyboardInterrupt:
        logger.info("用户停止服务器")
        return True
    except Exception as e:
        logger.error(f"启动MCP服务器失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    try:
        from config import DOCUMENT_FOLDER, QDRANT_PATH
        
        os.makedirs(DOCUMENT_FOLDER, exist_ok=True)
        os.makedirs(QDRANT_PATH, exist_ok=True)
        
        logger.info(f"✓ 目录检查完成")
        logger.info(f"  文档目录: {DOCUMENT_FOLDER}")
        logger.info(f"  数据库目录: {QDRANT_PATH}")
        return True
        
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("MCP Qdrant文档向量化工具 - 自动启动")
    print("=" * 60)
    
    steps = [
        ("检查Python环境", check_python),
        ("安装依赖包", install_dependencies),
        ("检查必需模块", check_required_modules),
        ("创建必要目录", create_directories),
        ("检查Ollama服务", run_ollama_check),
        ("运行系统测试", run_system_test),
        ("启动MCP服务器", start_mcp_server),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n{'='*20} {step_name} {'='*20}")
        
        try:
            if not step_func():
                logger.error(f"步骤失败: {step_name}")
                
                # 对于某些步骤，失败后询问是否继续
                if step_name in ["运行系统测试"]:
                    continue
                elif step_name in ["检查Ollama服务"]:
                    logger.error("Ollama服务检查失败，无法继续")
                    logger.info("请手动启动Ollama服务: python start_ollama.py")
                    return 1
                else:
                    return 1
                    
        except Exception as e:
            logger.error(f"步骤异常 {step_name}: {e}")
            return 1
    
    logger.info("\n" + "=" * 60)
    logger.info("✓ 所有步骤完成!")
    logger.info("=" * 60)
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        if exit_code != 0:
            input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序异常: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
