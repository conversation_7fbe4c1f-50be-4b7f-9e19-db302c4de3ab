"""
配置文件
"""
import os
from pathlib import Path

# 文档监控路径
DOCUMENT_FOLDER = r"E:\Desktop\obsidian"

# Qdrant数据库路径
QDRANT_PATH = r"D:\work\project1\qdrant"

# Qdrant配置
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
QDRANT_COLLECTION_NAME = "documents"

# Ollama配置
OLLAMA_HOST = "http://localhost:11434"
EMBEDDING_MODEL = "mxbai-embed-large:latest"

# 支持的文档格式
SUPPORTED_EXTENSIONS = {
    '.txt', '.md', '.pdf', '.docx', '.doc', 
    '.rtf', '.odt', '.html', '.htm', '.json'
}

# 向量维度 (mxbai-embed-large 的维度)
VECTOR_DIMENSION = 1024

# 文件监控配置
MONITOR_RECURSIVE = True
MONITOR_IGNORE_PATTERNS = ['*.tmp', '*.swp', '.*', '__pycache__']

# MCP服务器配置
MCP_SERVER_NAME = "qdrant-document-server"
MCP_SERVER_VERSION = "1.0.0"

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 确保目录存在
def ensure_directories():
    """确保必要的目录存在"""
    os.makedirs(DOCUMENT_FOLDER, exist_ok=True)
    os.makedirs(QDRANT_PATH, exist_ok=True)

if __name__ == "__main__":
    ensure_directories()
    print(f"文档文件夹: {DOCUMENT_FOLDER}")
    print(f"Qdrant路径: {QDRANT_PATH}")
