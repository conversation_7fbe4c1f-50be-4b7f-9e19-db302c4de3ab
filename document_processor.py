"""
文档处理模块
"""
import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import PyPDF2
import docx
import markdown
import json
from bs4 import BeautifulSoup
from config import SUPPORTED_EXTENSIONS

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器类"""
    
    def __init__(self):
        self.processors = {
            '.txt': self._process_txt,
            '.md': self._process_markdown,
            '.pdf': self._process_pdf,
            '.docx': self._process_docx,
            '.doc': self._process_docx,
            '.html': self._process_html,
            '.htm': self._process_html,
            '.json': self._process_json,
        }
    
    def is_supported(self, file_path: str) -> bool:
        """检查文件是否支持处理"""
        return Path(file_path).suffix.lower() in SUPPORTED_EXTENSIONS
    
    async def process_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """处理文件并提取内容"""
        try:
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return None
            
            if not self.is_supported(file_path):
                logger.warning(f"不支持的文件格式: {file_path}")
                return None
            
            file_ext = Path(file_path).suffix.lower()
            processor = self.processors.get(file_ext)
            
            if not processor:
                logger.error(f"没有找到处理器: {file_ext}")
                return None
            
            content = await processor(file_path)
            if not content:
                return None
            
            # 获取文件元数据
            stat = os.stat(file_path)
            
            return {
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'file_ext': file_ext,
                'content': content,
                'size': stat.st_size,
                'modified_time': stat.st_mtime,
                'created_time': stat.st_ctime,
            }
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            return None
    
    async def _process_txt(self, file_path: str) -> Optional[str]:
        """处理文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except Exception as e:
                logger.error(f"读取文本文件失败 {file_path}: {e}")
                return None
        except Exception as e:
            logger.error(f"处理文本文件失败 {file_path}: {e}")
            return None
    
    async def _process_markdown(self, file_path: str) -> Optional[str]:
        """处理Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # 转换为HTML然后提取纯文本
            html = markdown.markdown(md_content)
            soup = BeautifulSoup(html, 'html.parser')
            return soup.get_text()
            
        except Exception as e:
            logger.error(f"处理Markdown文件失败 {file_path}: {e}")
            return None
    
    async def _process_pdf(self, file_path: str) -> Optional[str]:
        """处理PDF文件"""
        try:
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
                
        except Exception as e:
            logger.error(f"处理PDF文件失败 {file_path}: {e}")
            return None
    
    async def _process_docx(self, file_path: str) -> Optional[str]:
        """处理Word文档"""
        try:
            doc = docx.Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
            
        except Exception as e:
            logger.error(f"处理Word文档失败 {file_path}: {e}")
            return None
    
    async def _process_html(self, file_path: str) -> Optional[str]:
        """处理HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            return soup.get_text()
            
        except Exception as e:
            logger.error(f"处理HTML文件失败 {file_path}: {e}")
            return None
    
    async def _process_json(self, file_path: str) -> Optional[str]:
        """处理JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 将JSON转换为可读的文本格式
            return json.dumps(data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"处理JSON文件失败 {file_path}: {e}")
            return None

# 全局文档处理器实例
document_processor = DocumentProcessor()

async def main():
    """测试文档处理器"""
    test_files = [
        "test.txt",
        "test.md", 
        "test.pdf",
        "test.docx"
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            result = await document_processor.process_file(file_path)
            if result:
                print(f"处理成功: {file_path}")
                print(f"内容长度: {len(result['content'])}")
            else:
                print(f"处理失败: {file_path}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
