"""
Ollama嵌入服务
"""
import asyncio
import logging
from typing import List, Optional
import ollama
from config import OLLAMA_HOST, EMBEDDING_MODEL

logger = logging.getLogger(__name__)

class EmbeddingService:
    """Ollama嵌入服务类"""
    
    def __init__(self):
        self.client = ollama.Client(host=OLLAMA_HOST)
        self.model = EMBEDDING_MODEL
        
    async def check_model_availability(self) -> bool:
        """检查嵌入模型是否可用"""
        try:
            models = self.client.list()
            model_names = [model['name'] for model in models['models']]
            return self.model in model_names
        except Exception as e:
            logger.error(f"检查模型可用性失败: {e}")
            return False
    
    async def pull_model_if_needed(self) -> bool:
        """如果需要，拉取嵌入模型"""
        try:
            if not await self.check_model_availability():
                logger.info(f"正在拉取模型 {self.model}...")
                self.client.pull(self.model)
                logger.info(f"模型 {self.model} 拉取完成")
            return True
        except Exception as e:
            logger.error(f"拉取模型失败: {e}")
            return False
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本的嵌入向量"""
        try:
            if not text.strip():
                return None

            # 检查Ollama服务是否可用
            if not await self.test_connection():
                logger.error("Ollama服务不可用，请确保服务正在运行")
                return None

            response = self.client.embeddings(
                model=self.model,
                prompt=text
            )

            if 'embedding' in response:
                return response['embedding']
            else:
                logger.error("嵌入响应中没有embedding字段")
                return None

        except Exception as e:
            logger.error(f"获取嵌入向量失败: {e}")
            logger.error("请检查:")
            logger.error("1. Ollama服务是否运行 (ollama serve)")
            logger.error("2. 嵌入模型是否可用")
            logger.error("3. 网络连接是否正常")
            return None
    
    async def get_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """批量获取文本的嵌入向量"""
        embeddings = []
        for text in texts:
            embedding = await self.get_embedding(text)
            embeddings.append(embedding)
            # 添加小延迟避免过载
            await asyncio.sleep(0.1)
        return embeddings
    
    async def test_connection(self) -> bool:
        """测试与Ollama的连接"""
        try:
            test_text = "测试连接"
            embedding = await self.get_embedding(test_text)
            return embedding is not None
        except Exception as e:
            logger.error(f"测试连接失败: {e}")
            return False

# 全局嵌入服务实例
embedding_service = EmbeddingService()

async def main():
    """测试嵌入服务"""
    print("测试嵌入服务...")
    
    # 检查模型可用性
    available = await embedding_service.check_model_availability()
    print(f"模型可用性: {available}")
    
    if not available:
        print("正在拉取模型...")
        success = await embedding_service.pull_model_if_needed()
        if not success:
            print("拉取模型失败")
            return
    
    # 测试嵌入
    test_text = "这是一个测试文档"
    embedding = await embedding_service.get_embedding(test_text)
    if embedding:
        print(f"嵌入向量维度: {len(embedding)}")
        print(f"前5个值: {embedding[:5]}")
    else:
        print("获取嵌入向量失败")

if __name__ == "__main__":
    asyncio.run(main())
