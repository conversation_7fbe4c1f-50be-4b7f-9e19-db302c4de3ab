"""
文件监控服务
"""
import asyncio
import logging
from pathlib import Path
from typing import Set
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent
from document_processor import document_processor
from embedding_service import embedding_service
from vector_store import vector_store
from config import DOCUMENT_FOLDER, MONITOR_RECURSIVE, MONITOR_IGNORE_PATTERNS

logger = logging.getLogger(__name__)

class DocumentEventHandler(FileSystemEventHandler):
    """文档事件处理器"""
    
    def __init__(self):
        super().__init__()
        self.processing_files: Set[str] = set()
        
    def should_ignore(self, file_path: str) -> bool:
        """检查是否应该忽略文件"""
        path = Path(file_path)
        
        # 检查忽略模式
        for pattern in MONITOR_IGNORE_PATTERNS:
            if path.match(pattern):
                return True
        
        # 检查是否为支持的文件格式
        if not document_processor.is_supported(file_path):
            return True
            
        return False
    
    def on_created(self, event: FileSystemEvent):
        """文件创建事件"""
        if not event.is_directory and not self.should_ignore(event.src_path):
            logger.info(f"检测到新文件: {event.src_path}")
            asyncio.create_task(self.process_file(event.src_path, "created"))
    
    def on_modified(self, event: FileSystemEvent):
        """文件修改事件"""
        if not event.is_directory and not self.should_ignore(event.src_path):
            logger.info(f"检测到文件修改: {event.src_path}")
            asyncio.create_task(self.process_file(event.src_path, "modified"))
    
    def on_deleted(self, event: FileSystemEvent):
        """文件删除事件"""
        if not event.is_directory and not self.should_ignore(event.src_path):
            logger.info(f"检测到文件删除: {event.src_path}")
            asyncio.create_task(self.delete_file(event.src_path))
    
    def on_moved(self, event: FileSystemEvent):
        """文件移动事件"""
        if hasattr(event, 'dest_path'):
            # 处理文件重命名/移动
            if not event.is_directory:
                if not self.should_ignore(event.src_path):
                    logger.info(f"检测到文件移动: {event.src_path} -> {event.dest_path}")
                    asyncio.create_task(self.delete_file(event.src_path))
                
                if not self.should_ignore(event.dest_path):
                    asyncio.create_task(self.process_file(event.dest_path, "moved"))
    
    async def process_file(self, file_path: str, event_type: str):
        """处理文件"""
        try:
            # 避免重复处理
            if file_path in self.processing_files:
                return
            
            self.processing_files.add(file_path)
            
            try:
                # 处理文档
                doc_data = await document_processor.process_file(file_path)
                if not doc_data:
                    logger.warning(f"文档处理失败: {file_path}")
                    return
                
                # 获取嵌入向量
                embedding = await embedding_service.get_embedding(doc_data['content'])
                if not embedding:
                    logger.warning(f"获取嵌入向量失败: {file_path}")
                    return
                
                # 存储到向量数据库
                if event_type == "modified":
                    success = await vector_store.update_document(doc_data, embedding)
                else:
                    success = await vector_store.add_document(doc_data, embedding)
                
                if success:
                    logger.info(f"文档处理完成: {file_path} ({event_type})")
                else:
                    logger.error(f"向量存储失败: {file_path}")
                    
            finally:
                self.processing_files.discard(file_path)
                
        except Exception as e:
            logger.error(f"处理文件异常 {file_path}: {e}")
            self.processing_files.discard(file_path)
    
    async def delete_file(self, file_path: str):
        """删除文件"""
        try:
            success = await vector_store.delete_document(file_path)
            if success:
                logger.info(f"文档删除完成: {file_path}")
            else:
                logger.warning(f"文档删除失败: {file_path}")
                
        except Exception as e:
            logger.error(f"删除文件异常 {file_path}: {e}")

class FileMonitor:
    """文件监控器"""
    
    def __init__(self):
        self.observer = Observer()
        self.event_handler = DocumentEventHandler()
        self.is_running = False
    
    async def start(self):
        """启动文件监控"""
        try:
            # 确保监控目录存在
            Path(DOCUMENT_FOLDER).mkdir(parents=True, exist_ok=True)
            
            # 设置监控
            self.observer.schedule(
                self.event_handler,
                DOCUMENT_FOLDER,
                recursive=MONITOR_RECURSIVE
            )
            
            self.observer.start()
            self.is_running = True
            
            logger.info(f"文件监控已启动: {DOCUMENT_FOLDER}")
            
            # 初始扫描现有文件
            await self.scan_existing_files()
            
        except Exception as e:
            logger.error(f"启动文件监控失败: {e}")
            raise
    
    async def stop(self):
        """停止文件监控"""
        if self.is_running:
            self.observer.stop()
            self.observer.join()
            self.is_running = False
            logger.info("文件监控已停止")
    
    async def scan_existing_files(self):
        """扫描现有文件"""
        try:
            logger.info("开始扫描现有文件...")
            
            folder_path = Path(DOCUMENT_FOLDER)
            if not folder_path.exists():
                logger.warning(f"监控文件夹不存在: {DOCUMENT_FOLDER}")
                return
            
            # 获取所有支持的文件
            files_to_process = []
            
            if MONITOR_RECURSIVE:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in folder_path.glob(pattern):
                if file_path.is_file() and document_processor.is_supported(str(file_path)):
                    if not self.event_handler.should_ignore(str(file_path)):
                        files_to_process.append(str(file_path))
            
            logger.info(f"找到 {len(files_to_process)} 个文件需要处理")
            
            # 批量处理文件
            for file_path in files_to_process:
                await self.event_handler.process_file(file_path, "scan")
                # 添加小延迟避免过载
                await asyncio.sleep(0.1)
            
            logger.info("现有文件扫描完成")
            
        except Exception as e:
            logger.error(f"扫描现有文件失败: {e}")

# 全局文件监控器实例
file_monitor = FileMonitor()

async def main():
    """测试文件监控器"""
    print("测试文件监控器...")
    
    try:
        await file_monitor.start()
        print("文件监控已启动，按Ctrl+C停止...")
        
        # 保持运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("正在停止文件监控...")
        await file_monitor.stop()
        print("文件监控已停止")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
