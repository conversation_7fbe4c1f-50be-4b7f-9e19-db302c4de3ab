@echo off
chcp 65001 >nul
echo 正在查找Python安装...
echo.

REM 方法1: 检查 python 命令
echo 检查 python 命令...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ 找到: python
    python --version
    echo 可以使用: python
    goto :end
)

REM 方法2: 检查 py 命令
echo 检查 py 命令...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ 找到: py
    py --version
    echo 可以使用: py
    goto :end
)

REM 方法3: 检查常见路径
echo 检查常见安装路径...

set PATHS=C:\Python39\python.exe;C:\Python310\python.exe;C:\Python311\python.exe;C:\Python312\python.exe;C:\Python313\python.exe

for %%P in (%PATHS%) do (
    if exist "%%P" (
        echo ✓ 找到: %%P
        "%%P" --version
        echo 可以使用: "%%P"
        goto :end
    )
)

REM 方法4: 检查用户目录
echo 检查用户目录...
set USER_PATHS=%USERPROFILE%\AppData\Local\Programs\Python\Python39\python.exe;%USERPROFILE%\AppData\Local\Programs\Python\Python310\python.exe;%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe;%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe;%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe

for %%P in (%USER_PATHS%) do (
    if exist "%%P" (
        echo ✓ 找到: %%P
        "%%P" --version
        echo 可以使用: "%%P"
        goto :end
    )
)

REM 方法5: 使用where命令
echo 使用where命令查找...
where python >nul 2>&1
if not errorlevel 1 (
    echo ✓ 系统PATH中找到python:
    where python
    goto :end
)

where py >nul 2>&1
if not errorlevel 1 (
    echo ✓ 系统PATH中找到py:
    where py
    goto :end
)

echo.
echo ✗ 未找到Python安装
echo.
echo 解决方案:
echo 1. 从 https://python.org 下载Python 3.8+
echo 2. 安装时勾选 "Add Python to PATH"
echo 3. 或者手动添加Python到系统PATH
echo.

:end
echo.
pause
