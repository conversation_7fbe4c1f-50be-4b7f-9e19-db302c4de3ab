#!/usr/bin/env python3
"""
修复502错误的专用脚本
"""
import os
import sys
import subprocess
import time
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def check_ollama_process():
    """检查Ollama进程是否运行"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq ollama.exe'], 
                                  capture_output=True, text=True)
            if 'ollama.exe' in result.stdout:
                logger.info("✓ 发现Ollama进程正在运行")
                return True
            else:
                logger.info("✗ Ollama进程未运行")
                return False
    except Exception as e:
        logger.error(f"检查进程失败: {e}")
        return False

def kill_ollama_processes():
    """终止所有Ollama进程"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/IM', 'ollama.exe', '/F'], 
                         capture_output=True, text=True)
            logger.info("已终止所有Ollama进程")
            time.sleep(2)
            return True
    except Exception as e:
        logger.error(f"终止进程失败: {e}")
        return False

def start_ollama_fresh():
    """重新启动Ollama服务"""
    ollama_path = r"D:\ollama\ollama.exe"
    
    if not os.path.exists(ollama_path):
        logger.error(f"Ollama不存在: {ollama_path}")
        return False
    
    try:
        logger.info("正在启动Ollama服务...")
        
        # 启动服务
        if os.name == 'nt':  # Windows
            process = subprocess.Popen([ollama_path, "serve"], 
                                     creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            process = subprocess.Popen([ollama_path, "serve"])
        
        # 等待服务启动
        logger.info("等待服务启动...")
        for i in range(30):
            time.sleep(1)
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=3)
                if response.status_code == 200:
                    logger.info("✓ Ollama服务启动成功!")
                    return True
            except:
                pass
            print(f"等待中... ({i+1}/30)", end='\r')
        
        logger.error("✗ Ollama服务启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动Ollama失败: {e}")
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✓ Ollama连接正常")
            data = response.json()
            models = data.get('models', [])
            logger.info(f"可用模型数量: {len(models)}")
            
            # 检查嵌入模型
            model_names = [model.get('name', '') for model in models]
            if 'mxbai-embed-large:latest' in model_names:
                logger.info("✓ 嵌入模型可用")
            else:
                logger.warning("⚠ 嵌入模型不可用")
                logger.info(f"可用模型: {model_names}")
            
            return True
        else:
            logger.error(f"Ollama响应错误: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error("无法连接到Ollama服务")
        return False
    except Exception as e:
        logger.error(f"测试连接失败: {e}")
        return False

def check_port_conflict():
    """检查端口冲突"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for line in lines:
                if ':11434' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        logger.info(f"端口11434被PID {pid}占用")
                        
                        # 获取进程名
                        try:
                            proc_result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'], 
                                                       capture_output=True, text=True)
                            for proc_line in proc_result.stdout.split('\n'):
                                if pid in proc_line:
                                    proc_name = proc_line.split()[0]
                                    logger.info(f"占用进程: {proc_name}")
                                    return pid, proc_name
                        except:
                            pass
                        return pid, "Unknown"
            
            logger.info("端口11434未被占用")
            return None, None
    except Exception as e:
        logger.error(f"检查端口失败: {e}")
        return None, None

def main():
    """主修复函数"""
    print("=" * 50)
    print("修复502 Bad Gateway错误")
    print("=" * 50)
    
    # 1. 检查当前连接状态
    logger.info("1. 检查当前Ollama连接状态...")
    if test_ollama_connection():
        logger.info("✓ Ollama服务正常，无需修复")
        return 0
    
    # 2. 检查进程状态
    logger.info("\n2. 检查Ollama进程状态...")
    process_running = check_ollama_process()
    
    # 3. 检查端口占用
    logger.info("\n3. 检查端口占用...")
    pid, proc_name = check_port_conflict()
    
    # 4. 如果有进程但服务不响应，清理进程
    if process_running or pid:
        logger.info("\n4. 清理现有进程...")
        kill_ollama_processes()
        time.sleep(3)
    
    # 5. 重新启动服务
    logger.info("\n5. 重新启动Ollama服务...")
    if start_ollama_fresh():
        logger.info("\n6. 验证修复结果...")
        if test_ollama_connection():
            logger.info("\n" + "=" * 50)
            logger.info("✓ 502错误已修复!")
            logger.info("现在可以运行: python test_system.py")
            logger.info("或运行: python start_server.py")
            logger.info("=" * 50)
            return 0
    
    logger.error("\n" + "=" * 50)
    logger.error("✗ 无法修复502错误")
    logger.error("建议:")
    logger.error("1. 重启电脑")
    logger.error("2. 检查Ollama安装")
    logger.error("3. 检查防火墙设置")
    logger.error("=" * 50)
    return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
