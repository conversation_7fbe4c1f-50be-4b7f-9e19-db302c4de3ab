#!/usr/bin/env python3
"""
修复Ollama连接问题的脚本
"""
import os
import sys
import time
import subprocess
import logging
import requests
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_ollama_process():
    """检查Ollama进程是否运行"""
    try:
        # Windows下检查进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq ollama.exe'], 
                              capture_output=True, text=True)
        if 'ollama.exe' in result.stdout:
            logger.info("✓ Ollama进程正在运行")
            return True
        else:
            logger.warning("✗ Ollama进程未运行")
            return False
    except Exception as e:
        logger.error(f"检查进程失败: {e}")
        return False

def check_ollama_service():
    """检查Ollama服务是否响应"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✓ Ollama服务响应正常")
            return True
        else:
            logger.warning(f"✗ Ollama服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.warning("✗ 无法连接到Ollama服务 (端口11434)")
        return False
    except Exception as e:
        logger.error(f"检查服务失败: {e}")
        return False

def start_ollama_service():
    """启动Ollama服务"""
    ollama_exe = r"D:\ollama\ollama.exe"
    
    if not os.path.exists(ollama_exe):
        logger.error(f"Ollama可执行文件不存在: {ollama_exe}")
        return False
    
    try:
        logger.info("正在启动Ollama服务...")
        # 在后台启动ollama serve
        process = subprocess.Popen([ollama_exe, "serve"], 
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # 等待服务启动
        logger.info("等待服务启动...")
        for i in range(30):  # 等待最多30秒
            time.sleep(1)
            if check_ollama_service():
                logger.info("✓ Ollama服务启动成功")
                return True
            print(f"等待中... ({i+1}/30)", end='\r')
        
        logger.error("✗ Ollama服务启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动Ollama服务失败: {e}")
        return False

def check_embedding_model():
    """检查嵌入模型"""
    ollama_exe = r"D:\ollama\ollama.exe"
    
    try:
        result = subprocess.run([ollama_exe, "list"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info("可用模型:")
            print(result.stdout)
            
            if "mxbai-embed-large" in result.stdout:
                logger.info("✓ 嵌入模型 mxbai-embed-large 可用")
                return True
            else:
                logger.warning("✗ 嵌入模型 mxbai-embed-large 不可用")
                logger.info("如果需要下载模型，请运行:")
                logger.info(f"{ollama_exe} pull mxbai-embed-large:latest")
                return False
        else:
            logger.error("无法获取模型列表")
            return False
            
    except Exception as e:
        logger.error(f"检查模型失败: {e}")
        return False

def test_embedding():
    """测试嵌入功能"""
    try:
        import ollama
        client = ollama.Client(host="http://localhost:11434")
        
        logger.info("测试嵌入功能...")
        response = client.embeddings(
            model="mxbai-embed-large:latest",
            prompt="测试文本"
        )
        
        if 'embedding' in response:
            embedding = response['embedding']
            logger.info(f"✓ 嵌入测试成功，向量维度: {len(embedding)}")
            return True
        else:
            logger.error("✗ 嵌入响应格式错误")
            return False
            
    except Exception as e:
        logger.error(f"✗ 嵌入测试失败: {e}")
        return False

def main():
    """主修复函数"""
    logger.info("=" * 50)
    logger.info("Ollama连接问题修复工具")
    logger.info("=" * 50)
    
    # 1. 检查进程
    logger.info("\n1. 检查Ollama进程...")
    process_running = check_ollama_process()
    
    # 2. 检查服务
    logger.info("\n2. 检查Ollama服务...")
    service_running = check_ollama_service()
    
    # 3. 如果服务未运行，尝试启动
    if not service_running:
        logger.info("\n3. 尝试启动Ollama服务...")
        if not start_ollama_service():
            logger.error("无法启动Ollama服务，请手动启动:")
            logger.error("运行: start_ollama.bat")
            logger.error("或: D:\\ollama\\ollama.exe serve")
            return 1
    
    # 4. 检查模型
    logger.info("\n4. 检查嵌入模型...")
    model_available = check_embedding_model()
    
    # 5. 测试嵌入
    if model_available:
        logger.info("\n5. 测试嵌入功能...")
        embedding_works = test_embedding()
        
        if embedding_works:
            logger.info("\n" + "=" * 50)
            logger.info("✓ 所有检查通过！Ollama连接正常")
            logger.info("现在可以运行: python start_server.py")
            logger.info("=" * 50)
            return 0
    
    logger.error("\n" + "=" * 50)
    logger.error("✗ 仍有问题需要解决")
    logger.error("请检查上述错误信息并手动修复")
    logger.error("=" * 50)
    return 1

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
