#!/usr/bin/env python3
"""
修复端口占用问题的脚本
"""
import subprocess
import requests
import time
import logging
import sys

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_port_usage(port=11434):
    """检查端口使用情况"""
    try:
        # Windows下检查端口占用
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    logger.info(f"端口 {port} 被进程 PID {pid} 占用")
                    return pid
        
        logger.info(f"端口 {port} 未被占用")
        return None
        
    except Exception as e:
        logger.error(f"检查端口失败: {e}")
        return None

def get_process_info(pid):
    """获取进程信息"""
    try:
        result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'], 
                              capture_output=True, text=True)
        lines = result.stdout.split('\n')
        for line in lines:
            if pid in line:
                parts = line.split()
                if len(parts) >= 1:
                    process_name = parts[0]
                    logger.info(f"进程 PID {pid}: {process_name}")
                    return process_name
        return None
    except Exception as e:
        logger.error(f"获取进程信息失败: {e}")
        return None

def test_ollama_service():
    """测试Ollama服务是否响应"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✓ Ollama服务响应正常")
            data = response.json()
            models = data.get('models', [])
            logger.info(f"可用模型数量: {len(models)}")
            for model in models:
                logger.info(f"  - {model.get('name', 'Unknown')}")
            return True
        else:
            logger.warning(f"Ollama服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.warning("无法连接到Ollama服务")
        return False
    except Exception as e:
        logger.error(f"测试Ollama服务失败: {e}")
        return False

def kill_process(pid):
    """终止进程"""
    try:
        subprocess.run(['taskkill', '/PID', pid, '/F'], check=True)
        logger.info(f"已终止进程 PID {pid}")
        return True
    except Exception as e:
        logger.error(f"终止进程失败: {e}")
        return False

def start_ollama_service():
    """启动Ollama服务"""
    ollama_exe = r"D:\ollama\ollama.exe"
    
    try:
        logger.info("正在启动Ollama服务...")
        process = subprocess.Popen([ollama_exe, "serve"], 
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        # 等待服务启动
        for i in range(15):
            time.sleep(1)
            if test_ollama_service():
                logger.info("✓ Ollama服务启动成功")
                return True
            print(f"等待服务启动... ({i+1}/15)", end='\r')
        
        logger.error("Ollama服务启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动Ollama服务失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("端口占用问题修复工具")
    logger.info("=" * 50)
    
    # 1. 检查端口占用
    logger.info("\n1. 检查端口11434占用情况...")
    pid = check_port_usage(11434)
    
    # 2. 测试Ollama服务
    logger.info("\n2. 测试Ollama服务...")
    service_working = test_ollama_service()
    
    if service_working:
        logger.info("\n✓ Ollama服务正常运行，无需修复")
        logger.info("可以直接运行MCP服务器:")
        logger.info("python start_server.py")
        return 0
    
    # 3. 如果服务不工作但端口被占用
    if pid and not service_working:
        logger.info(f"\n3. 端口被占用但服务不响应，检查占用进程...")
        process_name = get_process_info(pid)
        
        if process_name and 'ollama' in process_name.lower():
            logger.info("发现僵死的Ollama进程，尝试终止...")
            if kill_process(pid):
                time.sleep(2)  # 等待进程完全终止
            else:
                logger.error("无法终止进程，请手动终止或重启电脑")
                return 1
        else:
            logger.warning(f"端口被其他进程占用: {process_name}")
            logger.info("请手动处理端口冲突或更改Ollama端口")
            return 1
    
    # 4. 尝试启动Ollama服务
    if not service_working:
        logger.info("\n4. 尝试启动Ollama服务...")
        if start_ollama_service():
            logger.info("\n✓ 问题已修复！")
            logger.info("现在可以运行: python start_server.py")
            return 0
        else:
            logger.error("\n✗ 无法启动Ollama服务")
            logger.info("请手动启动: D:\\ollama\\ollama.exe serve")
            return 1

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
