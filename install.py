#!/usr/bin/env python3
"""
安装脚本
"""
import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """运行命令"""
    try:
        logger.info(f"执行: {description or command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {command}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖"""
    logger.info("安装Python依赖包...")
    
    # 升级pip
    if not run_command("python -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command("pip install -r requirements.txt", "安装依赖包"):
        return False
    
    return True

def create_directories():
    """创建必要的目录"""
    from config import DOCUMENT_FOLDER, QDRANT_PATH
    
    directories = [DOCUMENT_FOLDER, QDRANT_PATH]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {directory}")
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            return False
    
    return True

def check_ollama():
    """检查Ollama"""
    logger.info("检查Ollama...")
    
    # 检查Ollama是否安装
    if not run_command("ollama --version", "检查Ollama版本"):
        logger.error("Ollama未安装或不在PATH中")
        logger.info("请访问 https://ollama.ai 安装Ollama")
        return False
    
    # 检查Ollama服务是否运行
    if not run_command("ollama list", "检查Ollama服务"):
        logger.error("Ollama服务未运行")
        logger.info("请运行: ollama serve")
        return False
    
    return True

def pull_embedding_model():
    """拉取嵌入模型"""
    from config import EMBEDDING_MODEL
    
    logger.info(f"拉取嵌入模型: {EMBEDDING_MODEL}")
    
    if not run_command(f"ollama pull {EMBEDDING_MODEL}", f"拉取模型 {EMBEDDING_MODEL}"):
        logger.error("拉取嵌入模型失败")
        return False
    
    return True

def create_example_documents():
    """创建示例文档"""
    from config import DOCUMENT_FOLDER
    
    examples = [
        ("example1.txt", "这是第一个示例文档。\n包含一些测试内容用于验证系统功能。"),
        ("example2.md", "# 示例Markdown文档\n\n这是一个**Markdown**格式的示例文档。\n\n- 列表项1\n- 列表项2"),
        ("example3.txt", "人工智能是计算机科学的一个分支。\n它致力于创建能够执行通常需要人类智能的任务的系统。")
    ]
    
    for filename, content in examples:
        file_path = Path(DOCUMENT_FOLDER) / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"创建示例文档: {filename}")
        except Exception as e:
            logger.error(f"创建示例文档失败 {filename}: {e}")
    
    return True

def main():
    """主安装函数"""
    logger.info("=" * 50)
    logger.info("MCP Qdrant文档向量化工具 - 安装程序")
    logger.info("=" * 50)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装Python依赖", install_dependencies),
        ("创建必要目录", create_directories),
        ("检查Ollama", check_ollama),
        ("拉取嵌入模型", pull_embedding_model),
        ("创建示例文档", create_example_documents),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n步骤: {step_name}")
        try:
            if not step_func():
                logger.error(f"步骤失败: {step_name}")
                return 1
        except Exception as e:
            logger.error(f"步骤异常 {step_name}: {e}")
            return 1
    
    logger.info("\n" + "=" * 50)
    logger.info("安装完成! 🎉")
    logger.info("=" * 50)
    logger.info("\n下一步:")
    logger.info("1. 运行系统测试: python test_system.py")
    logger.info("2. 启动服务器: python start_server.py")
    logger.info("3. 或使用批处理: start.bat")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    input("\n按回车键退出...")
    sys.exit(exit_code)
