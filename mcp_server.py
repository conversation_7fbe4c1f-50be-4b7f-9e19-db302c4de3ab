"""
MCP服务器主文件 - JSON-RPC实现
"""
import asyncio
import logging
import json
import sys
from typing import Any, Dict, List, Optional

from config import MCP_SERVER_NAME, MCP_SERVER_VERSION, LOG_LEVEL, LOG_FORMAT
from document_processor import document_processor
from embedding_service import embedding_service
from vector_store import vector_store
from file_monitor import file_monitor

# 配置日志
logging.basicConfig(level=LOG_LEVEL, format=LOG_FORMAT)
logger = logging.getLogger(__name__)

class MCPServer:
    """MCP服务器 - JSON-RPC实现"""

    def __init__(self):
        self.request_id = 0

    def get_tools_schema(self):
        """获取工具模式定义"""
        return [
            {
                "name": "search_documents",
                "description": "在向量数据库中搜索相关文档",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询文本"
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回结果数量限制",
                            "default": 5,
                            "minimum": 1,
                            "maximum": 20
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "add_document",
                "description": "手动添加文档到向量数据库",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "文档文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "delete_document",
                "description": "从向量数据库中删除文档",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "file_path": {
                            "type": "string",
                            "description": "要删除的文档文件路径"
                        }
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "list_documents",
                "description": "列出向量数据库中的所有文档",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            },
            {
                "name": "get_collection_info",
                "description": "获取向量数据库集合信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        ]

    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理JSON-RPC请求"""
        try:
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")

            if method == "initialize":
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {
                                "listChanged": True
                            }
                        },
                        "serverInfo": {
                            "name": MCP_SERVER_NAME,
                            "version": MCP_SERVER_VERSION
                        }
                    }
                }

            elif method == "tools/list":
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "tools": self.get_tools_schema()
                    }
                }

            elif method == "tools/call":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})

                result = await self.call_tool(tool_name, arguments)

                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result
                            }
                        ]
                    }
                }

            else:
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method not found: {method}"
                    }
                }

        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }

    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> str:
        """调用工具"""
        try:
            if name == "search_documents":
                return await self.search_documents(arguments)
            elif name == "add_document":
                return await self.add_document(arguments)
            elif name == "delete_document":
                return await self.delete_document(arguments)
            elif name == "list_documents":
                return await self.list_documents(arguments)
            elif name == "get_collection_info":
                return await self.get_collection_info(arguments)
            else:
                return f"未知工具: {name}"

        except Exception as e:
            logger.error(f"工具调用失败 {name}: {e}")
            return f"错误: {str(e)}"

    async def search_documents(self, arguments: Dict[str, Any]) -> str:
        """搜索文档"""
        try:
            query = arguments.get("query", "")
            limit = arguments.get("limit", 5)

            if not query.strip():
                return "搜索查询不能为空"

            # 获取查询的嵌入向量
            query_embedding = await embedding_service.get_embedding(query)
            if not query_embedding:
                return "获取查询嵌入向量失败"

            # 搜索相似文档
            results = await vector_store.search_similar(query_embedding, limit)

            if not results:
                return "未找到相关文档"

            # 格式化结果
            response_text = f"找到 {len(results)} 个相关文档:\n\n"

            for i, result in enumerate(results, 1):
                payload = result['payload']
                response_text += f"{i}. **{payload['file_name']}**\n"
                response_text += f"   相似度: {result['score']:.4f}\n"
                response_text += f"   路径: {payload['file_path']}\n"
                response_text += f"   内容预览: {payload['content'][:200]}...\n\n"

            return response_text

        except Exception as e:
            logger.error(f"搜索文档失败: {e}")
            return f"搜索失败: {str(e)}"

    async def add_document(self, arguments: Dict[str, Any]) -> str:
        """添加文档"""
        try:
            file_path = arguments.get("file_path", "")

            if not file_path:
                return "文件路径不能为空"

            # 处理文档
            doc_data = await document_processor.process_file(file_path)
            if not doc_data:
                return f"文档处理失败: {file_path}"

            # 获取嵌入向量
            embedding = await embedding_service.get_embedding(doc_data['content'])
            if not embedding:
                return f"获取嵌入向量失败: {file_path}"

            # 添加到向量数据库
            success = await vector_store.add_document(doc_data, embedding)

            if success:
                return f"文档添加成功: {doc_data['file_name']}"
            else:
                return f"文档添加失败: {file_path}"

        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return f"添加失败: {str(e)}"

    async def delete_document(self, arguments: Dict[str, Any]) -> str:
        """删除文档"""
        try:
            file_path = arguments.get("file_path", "")

            if not file_path:
                return "文件路径不能为空"

            success = await vector_store.delete_document(file_path)

            if success:
                return f"文档删除成功: {file_path}"
            else:
                return f"文档删除失败: {file_path}"

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return f"删除失败: {str(e)}"

    async def list_documents(self, arguments: Dict[str, Any]) -> str:
        """列出文档"""
        try:
            documents = await vector_store.get_all_documents()

            if not documents:
                return "向量数据库中没有文档"

            response_text = f"向量数据库中共有 {len(documents)} 个文档:\n\n"

            for i, doc in enumerate(documents, 1):
                payload = doc['payload']
                response_text += f"{i}. **{payload['file_name']}**\n"
                response_text += f"   路径: {payload['file_path']}\n"
                response_text += f"   大小: {payload['size']} 字节\n"
                response_text += f"   类型: {payload['file_ext']}\n\n"

            return response_text

        except Exception as e:
            logger.error(f"列出文档失败: {e}")
            return f"列出失败: {str(e)}"

    async def get_collection_info(self, arguments: Dict[str, Any]) -> str:
        """获取集合信息"""
        try:
            info = await vector_store.get_collection_info()

            if not info:
                return "获取集合信息失败"

            response_text = "向量数据库集合信息:\n\n"
            response_text += f"集合名称: {vector_store.collection_name}\n"
            response_text += f"向量维度: {info.get('name', 'N/A')}\n"
            response_text += f"向量数量: {info.get('vectors_count', 0)}\n"
            response_text += f"点数量: {info.get('points_count', 0)}\n"
            response_text += f"状态: {info.get('status', 'N/A')}\n"

            return response_text

        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return f"获取失败: {str(e)}"

    async def run(self):
        """运行服务器"""
        try:
            while True:
                # 读取JSON-RPC请求
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break

                try:
                    request = json.loads(line.strip())
                    response = await self.handle_request(request)

                    # 发送响应
                    print(json.dumps(response), flush=True)

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": "Parse error"
                        }
                    }
                    print(json.dumps(error_response), flush=True)

        except Exception as e:
            logger.error(f"服务器运行错误: {e}")

async def main():
    """主函数"""
    try:
        logger.info(f"启动MCP服务器: {MCP_SERVER_NAME} v{MCP_SERVER_VERSION}")

        # 初始化服务
        logger.info("初始化向量数据库...")
        await vector_store.initialize()

        logger.info("检查嵌入模型...")
        await embedding_service.pull_model_if_needed()

        logger.info("启动文件监控...")
        await file_monitor.start()

        # 创建并运行MCP服务器
        logger.info("启动MCP服务器...")
        server = MCPServer()
        await server.run()

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        raise
    finally:
        logger.info("停止文件监控...")
        await file_monitor.stop()
        logger.info("MCP服务器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
