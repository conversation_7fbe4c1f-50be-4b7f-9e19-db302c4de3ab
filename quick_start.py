#!/usr/bin/env python3
"""
快速启动脚本 - 最简单的启动方式
"""
import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("=" * 50)
    print("MCP Qdrant工具 - 快速启动")
    print("=" * 50)
    
    logger.info("Python版本: " + sys.version.split()[0])
    logger.info("工作目录: " + os.getcwd())
    
    # 检查必要文件
    required_files = ["start_server.py", "config.py", "requirements.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        logger.error(f"缺少必要文件: {missing_files}")
        logger.error("请确保在正确的项目目录中运行此脚本")
        return 1
    
    logger.info("✓ 必要文件检查通过")
    
    # 快速安装依赖
    logger.info("\n安装依赖包...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✓ 依赖包安装完成")
        else:
            logger.warning("⚠ 依赖包安装可能有问题，但继续尝试")
    except Exception as e:
        logger.warning(f"依赖包安装异常: {e}")
    
    # 检查Ollama连接
    logger.info("\n检查Ollama连接...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✓ Ollama服务正常")
        else:
            logger.warning("⚠ Ollama服务响应异常")
            logger.info("请先启动Ollama服务: python start_ollama.py")
            return 1
    except ImportError:
        logger.warning("requests模块未安装，跳过Ollama检查")
    except Exception as e:
        logger.warning(f"Ollama连接检查失败: {e}")
        logger.info("请先启动Ollama服务: python start_ollama.py")
        return 1
    
    # 启动MCP服务器
    logger.info("\n启动MCP服务器...")
    logger.info("按Ctrl+C停止服务器")
    
    try:
        subprocess.run([sys.executable, "start_server.py"])
        return 0
    except KeyboardInterrupt:
        logger.info("\n用户停止服务器")
        return 0
    except Exception as e:
        logger.error(f"启动失败: {e}")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        if exit_code != 0:
            input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
