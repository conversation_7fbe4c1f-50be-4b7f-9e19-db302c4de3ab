@echo off
chcp 65001 >nul
echo ========================================
echo 快速诊断测试
echo ========================================
echo.

echo 1. 检查Python安装...
echo.

REM 测试 py 命令
echo 测试 py 命令:
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ py 命令可用
    py --version
    set PYTHON_CMD=py
    goto :python_ok
) else (
    echo ✗ py 命令不可用
)

REM 测试 python 命令
echo 测试 python 命令:
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ python 命令可用
    python --version
    set PYTHON_CMD=python
    goto :python_ok
) else (
    echo ✗ python 命令不可用
)

REM 检查常见路径
echo 检查常见Python安装路径:
set FOUND=0

if exist "C:\Python39\python.exe" (
    echo ✓ 找到: C:\Python39\python.exe
    set PYTHON_CMD=C:\Python39\python.exe
    set FOUND=1
)

if exist "C:\Python310\python.exe" (
    echo ✓ 找到: C:\Python310\python.exe
    set PYTHON_CMD=C:\Python310\python.exe
    set FOUND=1
)

if exist "C:\Python311\python.exe" (
    echo ✓ 找到: C:\Python311\python.exe
    set PYTHON_CMD=C:\Python311\python.exe
    set FOUND=1
)

if exist "C:\Python312\python.exe" (
    echo ✓ 找到: C:\Python312\python.exe
    set PYTHON_CMD=C:\Python312\python.exe
    set FOUND=1
)

if exist "C:\Python313\python.exe" (
    echo ✓ 找到: C:\Python313\python.exe
    set PYTHON_CMD=C:\Python313\python.exe
    set FOUND=1
)

if %FOUND%==1 (
    echo 使用: %PYTHON_CMD%
    "%PYTHON_CMD%" --version
    goto :python_ok
)

echo ✗ 未找到Python安装
echo.
echo 请从 https://python.org 下载并安装Python
start https://python.org
goto :end

:python_ok
echo.
echo 2. 检查Ollama安装...
echo.

if exist "D:\ollama\ollama.exe" (
    echo ✓ 找到Ollama: D:\ollama\ollama.exe
    set OLLAMA_CMD=D:\ollama\ollama.exe
) else (
    echo ✗ 未找到Ollama: D:\ollama\ollama.exe
    echo 请检查Ollama安装路径
    goto :end
)

echo.
echo 3. 测试Ollama版本...
"%OLLAMA_CMD%" --version 2>nul
if errorlevel 1 (
    echo ✗ Ollama版本检查失败
) else (
    echo ✓ Ollama版本检查通过
)

echo.
echo 4. 测试Ollama模型列表...
"%OLLAMA_CMD%" list 2>nul
if errorlevel 1 (
    echo ✗ 无法获取模型列表（可能服务未运行）
) else (
    echo ✓ 模型列表获取成功
)

echo.
echo 5. 检查项目文件...
if exist "requirements.txt" (
    echo ✓ requirements.txt 存在
) else (
    echo ✗ requirements.txt 不存在
)

if exist "test_system.py" (
    echo ✓ test_system.py 存在
) else (
    echo ✗ test_system.py 不存在
)

if exist "start_server.py" (
    echo ✓ start_server.py 存在
) else (
    echo ✗ start_server.py 不存在
)

echo.
echo ========================================
echo 诊断完成
echo ========================================
echo.

if defined PYTHON_CMD (
    if defined OLLAMA_CMD (
        echo ✓ 基本环境检查通过
        echo.
        echo 建议的启动步骤:
        echo 1. 先运行: "%OLLAMA_CMD%" serve
        echo 2. 保持Ollama窗口打开
        echo 3. 在新窗口运行: "%PYTHON_CMD%" test_system.py
        echo 4. 然后运行: "%PYTHON_CMD%" start_server.py
    ) else (
        echo ✗ Ollama未找到，请检查安装
    )
) else (
    echo ✗ Python未找到，请先安装Python
)

:end
echo.
pause
