#!/usr/bin/env python3
"""
快速诊断测试脚本
"""
import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_python():
    """测试Python环境"""
    logger.info("1. Python环境检查")
    logger.info(f"   Python版本: {sys.version}")
    logger.info(f"   Python路径: {sys.executable}")
    logger.info(f"   工作目录: {os.getcwd()}")
    return True

def test_ollama():
    """测试Ollama安装"""
    logger.info("\n2. Ollama安装检查")
    
    ollama_paths = [
        r"D:\ollama\ollama.exe",
        r"C:\ollama\ollama.exe",
        os.path.join(os.path.expanduser("~"), "ollama", "ollama.exe"),
    ]
    
    found_ollama = None
    for path in ollama_paths:
        if os.path.exists(path):
            logger.info(f"   ✓ 找到Ollama: {path}")
            found_ollama = path
            break
    
    if not found_ollama:
        logger.error("   ✗ 未找到Ollama安装")
        return False
    
    # 测试版本
    try:
        result = subprocess.run([found_ollama, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            logger.info(f"   ✓ Ollama版本: {result.stdout.strip()}")
        else:
            logger.warning("   ⚠ 无法获取Ollama版本")
    except Exception as e:
        logger.warning(f"   ⚠ 版本检查失败: {e}")
    
    return True

def test_ollama_service():
    """测试Ollama服务"""
    logger.info("\n3. Ollama服务检查")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("   ✓ Ollama服务运行正常")
            data = response.json()
            models = data.get('models', [])
            logger.info(f"   ✓ 可用模型数量: {len(models)}")
            
            # 检查嵌入模型
            model_names = [model.get('name', '') for model in models]
            if 'mxbai-embed-large:latest' in model_names:
                logger.info("   ✓ 嵌入模型可用: mxbai-embed-large:latest")
            else:
                logger.warning("   ⚠ 嵌入模型未找到: mxbai-embed-large:latest")
                logger.info(f"   可用模型: {model_names}")
            
            return True
        else:
            logger.error(f"   ✗ Ollama服务响应异常: {response.status_code}")
            return False
    except ImportError:
        logger.warning("   ⚠ requests模块未安装，无法测试服务")
        return False
    except Exception as e:
        logger.error(f"   ✗ Ollama服务连接失败: {e}")
        return False

def test_project_files():
    """测试项目文件"""
    logger.info("\n4. 项目文件检查")
    
    required_files = [
        "requirements.txt", "config.py", "start_server.py", 
        "test_system.py", "mcp_server.py", "embedding_service.py",
        "vector_store.py", "document_processor.py", "file_monitor.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            logger.info(f"   ✓ {file}")
        else:
            logger.error(f"   ✗ {file}")
            missing_files.append(file)
    
    return len(missing_files) == 0

def test_dependencies():
    """测试依赖包"""
    logger.info("\n5. 依赖包检查")
    
    required_modules = [
        ('requests', 'HTTP请求'),
        ('ollama', 'Ollama客户端'),
        ('qdrant_client', 'Qdrant向量数据库'),
        ('watchdog', '文件监控'),
        ('PyPDF2', 'PDF处理'),
        ('docx', 'Word文档处理'),
        ('markdown', 'Markdown处理'),
        ('bs4', 'HTML处理'),
    ]
    
    missing_modules = []
    for module, desc in required_modules:
        try:
            __import__(module)
            logger.info(f"   ✓ {module} ({desc})")
        except ImportError:
            logger.error(f"   ✗ {module} ({desc})")
            missing_modules.append(module)
    
    if missing_modules:
        logger.info(f"\n   安装缺失模块: python -m pip install -r requirements.txt")
    
    return len(missing_modules) == 0

def test_directories():
    """测试目录配置"""
    logger.info("\n6. 目录配置检查")
    
    try:
        from config import DOCUMENT_FOLDER, QDRANT_PATH
        
        logger.info(f"   文档目录: {DOCUMENT_FOLDER}")
        if os.path.exists(DOCUMENT_FOLDER):
            logger.info("   ✓ 文档目录存在")
        else:
            logger.warning("   ⚠ 文档目录不存在，将自动创建")
            os.makedirs(DOCUMENT_FOLDER, exist_ok=True)
        
        logger.info(f"   数据库目录: {QDRANT_PATH}")
        if os.path.exists(QDRANT_PATH):
            logger.info("   ✓ 数据库目录存在")
        else:
            logger.warning("   ⚠ 数据库目录不存在，将自动创建")
            os.makedirs(QDRANT_PATH, exist_ok=True)
        
        return True
    except Exception as e:
        logger.error(f"   ✗ 目录配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("MCP Qdrant工具 - 快速诊断测试")
    print("=" * 60)
    
    tests = [
        ("Python环境", test_python),
        ("Ollama安装", test_ollama),
        ("Ollama服务", test_ollama_service),
        ("项目文件", test_project_files),
        ("依赖包", test_dependencies),
        ("目录配置", test_directories),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
    
    # 总结
    logger.info("\n" + "=" * 60)
    logger.info(f"诊断结果: {passed}/{total} 项通过")
    
    if passed == total:
        logger.info("✓ 所有检查通过! 系统准备就绪")
        logger.info("\n推荐启动命令:")
        logger.info("  python auto_start.py")
        return 0
    elif passed >= total - 2:
        logger.warning("⚠ 大部分检查通过，可以尝试启动")
        logger.info("\n推荐启动命令:")
        logger.info("  python start_ollama.py  # 先启动Ollama")
        logger.info("  python start_server.py  # 再启动MCP服务器")
        return 0
    else:
        logger.error("✗ 多项检查失败，需要修复问题")
        logger.info("\n建议:")
        logger.info("  1. 安装缺失的依赖: python -m pip install -r requirements.txt")
        logger.info("  2. 检查Ollama安装")
        logger.info("  3. 启动Ollama服务: python start_ollama.py")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
