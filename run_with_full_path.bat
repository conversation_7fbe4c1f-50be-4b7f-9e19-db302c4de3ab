@echo off
chcp 65001 >nul
echo ========================================
echo MCP Qdrant工具 - 完整路径运行
echo ========================================
echo.

REM 设置常见的Python路径
set PYTHON_PATHS[0]=python
set PYTHON_PATHS[1]=py
set PYTHON_PATHS[2]=C:\Python39\python.exe
set PYTHON_PATHS[3]=C:\Python310\python.exe
set PYTHON_PATHS[4]=C:\Python311\python.exe
set PYTHON_PATHS[5]=C:\Python312\python.exe
set PYTHON_PATHS[6]=C:\Python313\python.exe
set PYTHON_PATHS[7]=%USERPROFILE%\AppData\Local\Programs\Python\Python39\python.exe
set PYTHON_PATHS[8]=%USERPROFILE%\AppData\Local\Programs\Python\Python310\python.exe
set PYTHON_PATHS[9]=%USERPROFILE%\AppData\Local\Programs\Python\Python311\python.exe
set PYTHON_PATHS[10]=%USERPROFILE%\AppData\Local\Programs\Python\Python312\python.exe
set PYTHON_PATHS[11]=%USERPROFILE%\AppData\Local\Programs\Python\Python313\python.exe

set PYTHON_CMD=
set FOUND=0

echo 正在查找Python...
for /L %%i in (0,1,11) do (
    call set "CURRENT_PATH=%%PYTHON_PATHS[%%i]%%"
    call set "CURRENT_PATH=!CURRENT_PATH!"
    
    REM 测试当前路径
    "!CURRENT_PATH!" --version >nul 2>&1
    if not errorlevel 1 (
        set "PYTHON_CMD=!CURRENT_PATH!"
        set FOUND=1
        echo ✓ 找到Python: !CURRENT_PATH!
        "!CURRENT_PATH!" --version
        goto :found_python
    )
)

:not_found
echo ✗ 未找到Python
echo.
echo 请手动输入Python可执行文件的完整路径:
echo 例如: C:\Python311\python.exe
echo 或者: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
echo.
set /p PYTHON_CMD="Python路径: "

REM 验证用户输入的路径
"%PYTHON_CMD%" --version >nul 2>&1
if errorlevel 1 (
    echo ✗ 无效的Python路径，请重新运行脚本
    pause
    exit /b 1
)

:found_python
echo.
echo ========================================
echo 开始运行MCP工具
echo ========================================
echo.

echo 步骤1: 检查Ollama连接...
"%PYTHON_CMD%" fix_ollama_connection.py
if errorlevel 1 (
    echo.
    echo ✗ Ollama连接失败
    echo 请先启动Ollama服务:
    echo 1. 运行 start_ollama_service.bat
    echo 2. 或手动运行: D:\ollama\ollama.exe serve
    echo.
    pause
    exit /b 1
)

echo.
echo 步骤2: 运行系统测试...
"%PYTHON_CMD%" test_system.py
if errorlevel 1 (
    echo.
    echo ✗ 系统测试失败，但可以尝试继续
    echo.
)

echo.
echo 步骤3: 启动MCP服务器...
echo 按Ctrl+C停止服务器
echo.
"%PYTHON_CMD%" start_server.py

pause
