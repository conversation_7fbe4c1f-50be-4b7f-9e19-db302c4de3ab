@echo off
chcp 65001 >nul
echo ========================================
echo 环境设置和问题修复工具
echo ========================================
echo.

echo 1. 检查Python安装...
echo.

REM 尝试不同的Python命令
set PYTHON_CMD=
set PYTHON_FOUND=0

REM 检查 python
python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo ✓ 找到Python命令: python
    python --version
    goto :python_found
)

REM 检查 py
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo ✓ 找到Python命令: py
    py --version
    goto :python_found
)

REM 检查常见安装路径
set COMMON_PATHS[0]=C:\Python39\python.exe
set COMMON_PATHS[1]=C:\Python310\python.exe
set COMMON_PATHS[2]=C:\Python311\python.exe
set COMMON_PATHS[3]=C:\Python312\python.exe
set COMMON_PATHS[4]=C:\Python313\python.exe
set COMMON_PATHS[5]=C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe
set COMMON_PATHS[6]=C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
set COMMON_PATHS[7]=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
set COMMON_PATHS[8]=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe
set COMMON_PATHS[9]=C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe

for /L %%i in (0,1,9) do (
    call set "PYTHON_PATH=%%COMMON_PATHS[%%i]%%"
    if exist "!PYTHON_PATH!" (
        set PYTHON_CMD=!PYTHON_PATH!
        set PYTHON_FOUND=1
        echo ✓ 找到Python: !PYTHON_PATH!
        "!PYTHON_PATH!" --version
        goto :python_found
    )
)

REM 如果都没找到，提示用户
if %PYTHON_FOUND%==0 (
    echo ✗ 未找到Python安装
    echo.
    echo 请选择以下选项:
    echo 1. 从 https://python.org 下载并安装Python
    echo 2. 手动输入Python可执行文件路径
    echo 3. 退出
    echo.
    choice /C 123 /M "请选择"
    
    if errorlevel 3 exit /b 1
    if errorlevel 2 goto :manual_python
    if errorlevel 1 (
        echo 请访问 https://python.org 下载Python 3.8或更高版本
        echo 安装时请勾选 "Add Python to PATH"
        pause
        exit /b 1
    )
)

:manual_python
echo.
set /p PYTHON_CMD="请输入Python可执行文件的完整路径: "
"%PYTHON_CMD%" --version >nul 2>&1
if errorlevel 1 (
    echo ✗ 无效的Python路径
    pause
    exit /b 1
)

:python_found
echo.
echo 2. 检查依赖包...
echo.

REM 检查pip
"%PYTHON_CMD%" -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ✗ pip未安装或不可用
    echo 正在尝试安装pip...
    "%PYTHON_CMD%" -m ensurepip --upgrade
    if errorlevel 1 (
        echo ✗ 无法安装pip
        pause
        exit /b 1
    )
)

echo ✓ pip可用
echo.

echo 3. 安装项目依赖...
echo.
"%PYTHON_CMD%" -m pip install -r requirements.txt
if errorlevel 1 (
    echo ✗ 依赖安装失败
    pause
    exit /b 1
)

echo ✓ 依赖安装完成
echo.

echo 4. 创建启动脚本...
echo.

REM 创建使用正确Python路径的启动脚本
echo @echo off > start_with_python.bat
echo chcp 65001 ^>nul >> start_with_python.bat
echo echo 启动MCP Qdrant工具... >> start_with_python.bat
echo echo. >> start_with_python.bat
echo "%PYTHON_CMD%" fix_ollama_connection.py >> start_with_python.bat
echo if errorlevel 1 ( >> start_with_python.bat
echo     echo Ollama连接检查失败，请先启动Ollama服务 >> start_with_python.bat
echo     pause >> start_with_python.bat
echo     exit /b 1 >> start_with_python.bat
echo ^) >> start_with_python.bat
echo echo. >> start_with_python.bat
echo echo 运行系统测试... >> start_with_python.bat
echo "%PYTHON_CMD%" test_system.py >> start_with_python.bat
echo if errorlevel 1 ( >> start_with_python.bat
echo     echo 系统测试失败 >> start_with_python.bat
echo     pause >> start_with_python.bat
echo     exit /b 1 >> start_with_python.bat
echo ^) >> start_with_python.bat
echo echo. >> start_with_python.bat
echo echo 启动MCP服务器... >> start_with_python.bat
echo "%PYTHON_CMD%" start_server.py >> start_with_python.bat
echo pause >> start_with_python.bat

echo ✓ 创建启动脚本: start_with_python.bat
echo.

echo 5. 创建Ollama启动脚本...
echo.

REM 创建Ollama启动脚本
echo @echo off > start_ollama_service.bat
echo chcp 65001 ^>nul >> start_ollama_service.bat
echo echo 启动Ollama服务... >> start_ollama_service.bat
echo echo 请保持此窗口打开！ >> start_ollama_service.bat
echo echo. >> start_ollama_service.bat
echo if exist "D:\ollama\ollama.exe" ( >> start_ollama_service.bat
echo     "D:\ollama\ollama.exe" serve >> start_ollama_service.bat
echo ^) else ( >> start_ollama_service.bat
echo     echo 错误: 找不到Ollama，请检查路径 D:\ollama\ollama.exe >> start_ollama_service.bat
echo     pause >> start_ollama_service.bat
echo ^) >> start_ollama_service.bat

echo ✓ 创建Ollama启动脚本: start_ollama_service.bat
echo.

echo ========================================
echo 环境设置完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 先运行: start_ollama_service.bat (保持窗口打开)
echo 2. 再运行: start_with_python.bat
echo.
echo 或者手动运行:
echo 1. "%PYTHON_CMD%" fix_ollama_connection.py
echo 2. "%PYTHON_CMD%" test_system.py  
echo 3. "%PYTHON_CMD%" start_server.py
echo.
pause
