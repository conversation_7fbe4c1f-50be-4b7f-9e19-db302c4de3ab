@echo off
chcp 65001 >nul
echo ========================================
echo MCP Qdrant工具 - 简单启动
echo ========================================
echo.

echo 检查Python...
py --version >nul 2>&1
if errorlevel 1 (
    echo ✗ 未找到py命令
    echo 请运行 run_with_full_path.bat 或 setup_environment.bat
    pause
    exit /b 1
)

echo ✓ Python可用
py --version
echo.

echo 安装依赖包...
py -m pip install -r requirements.txt
if errorlevel 1 (
    echo ✗ 依赖安装失败
    pause
    exit /b 1
)
echo.

echo 检查Ollama连接...
py fix_ollama_connection.py
if errorlevel 1 (
    echo.
    echo ✗ Ollama连接失败
    echo 请先启动Ollama服务:
    echo.
    echo 在新的命令行窗口运行:
    echo D:\ollama\ollama.exe serve
    echo.
    echo 然后重新运行此脚本
    pause
    exit /b 1
)
echo.

echo 运行系统测试...
py test_system.py
echo.

echo 启动MCP服务器...
echo 按Ctrl+C停止服务器
echo.
py start_server.py

pause
