@echo off
chcp 65001 >nul
echo ========================================
echo 解决端口11434占用问题
echo ========================================
echo.

echo 检查端口11434占用情况...
netstat -ano | findstr :11434
if errorlevel 1 (
    echo ✓ 端口11434未被占用
    goto :start_ollama
) else (
    echo ⚠ 端口11434被占用
)

echo.
echo 检查是否是Ollama进程占用...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :11434 ^| findstr LISTENING') do (
    set PID=%%a
    goto :check_process
)

:check_process
if not defined PID (
    echo 无法获取占用进程PID
    goto :manual_fix
)

echo 占用进程PID: %PID%
tasklist /FI "PID eq %PID%" | findstr ollama >nul
if not errorlevel 1 (
    echo ✓ 是Ollama进程占用，尝试终止...
    taskkill /PID %PID% /F >nul 2>&1
    if not errorlevel 1 (
        echo ✓ 已终止旧的Ollama进程
        timeout /t 2 >nul
        goto :start_ollama
    ) else (
        echo ✗ 无法终止进程，可能需要管理员权限
        goto :manual_fix
    )
) else (
    echo ⚠ 端口被其他进程占用
    tasklist /FI "PID eq %PID%"
    goto :manual_fix
)

:start_ollama
echo.
echo 启动Ollama服务...
if not exist "D:\ollama\ollama.exe" (
    echo ✗ 找不到Ollama: D:\ollama\ollama.exe
    pause
    exit /b 1
)

echo 正在启动Ollama服务...
start "Ollama Service" "D:\ollama\ollama.exe" serve

echo 等待服务启动...
timeout /t 5 >nul

echo 测试服务连接...
curl -s http://localhost:11434/api/tags >nul 2>&1
if not errorlevel 1 (
    echo ✓ Ollama服务启动成功
    echo.
    echo 现在可以运行MCP服务器:
    echo python start_server.py
) else (
    echo ⚠ 服务可能还在启动中，请稍等片刻
    echo 然后手动测试: curl http://localhost:11434/api/tags
)

goto :end

:manual_fix
echo.
echo ========================================
echo 需要手动处理
echo ========================================
echo.
echo 解决方案:
echo 1. 重启电脑（最简单）
echo 2. 以管理员身份运行此脚本
echo 3. 手动终止占用进程:
echo    taskkill /PID %PID% /F
echo 4. 更改Ollama端口（高级）
echo.

:end
pause
