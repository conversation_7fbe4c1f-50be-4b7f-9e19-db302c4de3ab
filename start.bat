@echo off
chcp 65001 >nul
echo ========================================
echo MCP Qdrant文档向量化工具
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
pip show mcp >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 检查Ollama服务...
D:\ollama\ollama.exe list >nul 2>&1
if errorlevel 1 (
    echo 警告: Ollama服务未运行
    echo 请先启动Ollama服务:
    echo 1. 运行 start_ollama.bat
    echo 2. 或手动运行: D:\ollama\ollama.exe serve
    echo.
    choice /C YN /M "是否继续启动MCP服务器"
    if errorlevel 2 exit /b 1
)

echo 运行系统测试...
python test_system.py
if errorlevel 1 (
    echo 警告: 系统测试未完全通过，但仍可尝试启动服务器
    echo.
)

echo 启动MCP服务器...
echo 按Ctrl+C停止服务器
echo.
python start_server.py

pause
