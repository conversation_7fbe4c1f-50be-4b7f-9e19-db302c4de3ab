@echo off
chcp 65001 >nul
echo ========================================
echo MCP Qdrant文档向量化工具
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 检查依赖包...
pip show mcp >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 运行系统测试...
python test_system.py
if errorlevel 1 (
    echo 警告: 系统测试未完全通过，但仍可尝试启动服务器
    echo.
)

echo 启动MCP服务器...
echo 按Ctrl+C停止服务器
echo.
python start_server.py

pause
