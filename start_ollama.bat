@echo off
chcp 65001 >nul
echo ========================================
echo 启动本地Ollama服务
echo ========================================
echo.

set OLLAMA_PATH=D:\ollama
set OLLAMA_EXE=%OLLAMA_PATH%\ollama.exe

echo 检查Ollama路径: %OLLAMA_PATH%
if not exist "%OLLAMA_EXE%" (
    echo 错误: 找不到Ollama可执行文件
    echo 路径: %OLLAMA_EXE%
    echo 请检查Ollama安装路径是否正确
    pause
    exit /b 1
)

echo 找到Ollama: %OLLAMA_EXE%
echo.

echo 检查Ollama版本...
"%OLLAMA_EXE%" --version
if errorlevel 1 (
    echo 警告: 无法获取Ollama版本
)
echo.

echo 检查可用模型...
"%OLLAMA_EXE%" list
if errorlevel 1 (
    echo 警告: 无法获取模型列表，可能服务未运行
)
echo.

echo 启动Ollama服务...
echo 注意: 请保持此窗口打开，Ollama服务将在此运行
echo 按Ctrl+C停止服务
echo.
"%OLLAMA_EXE%" serve

pause
