#!/usr/bin/env python3
"""
启动Ollama服务的Python脚本
"""
import os
import sys
import subprocess
import time
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def find_ollama():
    """查找Ollama可执行文件"""
    possible_paths = [
        r"D:\ollama\ollama.exe",
        r"C:\ollama\ollama.exe",
        os.path.join(os.path.expanduser("~"), "ollama", "ollama.exe"),
        os.path.join(os.environ.get("LOCALAPPDATA", ""), "Programs", "Ollama", "ollama.exe"),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            logger.info(f"✓ 找到Ollama: {path}")
            return path
    
    logger.error("✗ 未找到Ollama安装")
    logger.info("请检查以下路径:")
    for path in possible_paths:
        logger.info(f"  - {path}")
    
    # 让用户手动输入路径
    while True:
        user_path = input("\n请输入ollama.exe的完整路径 (或按回车退出): ").strip()
        if not user_path:
            return None
        if os.path.exists(user_path):
            return user_path
        logger.error(f"路径不存在: {user_path}")

def check_ollama_service():
    """检查Ollama服务是否运行"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            logger.info("✓ Ollama服务已运行")
            data = response.json()
            models = data.get('models', [])
            logger.info(f"可用模型数量: {len(models)}")
            for model in models:
                logger.info(f"  - {model.get('name', 'Unknown')}")
            return True
        else:
            logger.warning(f"Ollama服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.info("Ollama服务未运行")
        return False
    except Exception as e:
        logger.error(f"检查服务失败: {e}")
        return False

def check_port_usage():
    """检查端口11434占用情况"""
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for line in lines:
                if ':11434' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        logger.info(f"端口11434被进程PID {pid}占用")
                        
                        # 获取进程名
                        try:
                            proc_result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'], 
                                                       capture_output=True, text=True)
                            proc_lines = proc_result.stdout.split('\n')
                            for proc_line in proc_lines:
                                if pid in proc_line:
                                    proc_name = proc_line.split()[0]
                                    logger.info(f"占用进程: {proc_name}")
                                    return pid, proc_name
                        except:
                            pass
                        return pid, "Unknown"
            
            logger.info("端口11434未被占用")
            return None, None
    except Exception as e:
        logger.error(f"检查端口失败: {e}")
        return None, None

def kill_process(pid):
    """终止进程"""
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/PID', pid, '/F'], check=True)
            logger.info(f"已终止进程PID {pid}")
            return True
    except Exception as e:
        logger.error(f"终止进程失败: {e}")
        return False

def start_ollama_service(ollama_path):
    """启动Ollama服务"""
    try:
        logger.info("正在启动Ollama服务...")
        logger.info("重要: 请保持此窗口打开!")
        
        # 在新的控制台窗口启动Ollama
        if os.name == 'nt':  # Windows
            subprocess.Popen([ollama_path, "serve"], 
                           creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            subprocess.Popen([ollama_path, "serve"])
        
        # 等待服务启动
        logger.info("等待服务启动...")
        for i in range(30):
            time.sleep(1)
            if check_ollama_service():
                logger.info("✓ Ollama服务启动成功!")
                return True
            print(f"等待中... ({i+1}/30)", end='\r')
        
        logger.error("✗ Ollama服务启动超时")
        return False
        
    except Exception as e:
        logger.error(f"启动Ollama服务失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Ollama服务启动工具")
    print("=" * 50)
    
    # 1. 检查服务是否已运行
    logger.info("1. 检查Ollama服务状态...")
    if check_ollama_service():
        logger.info("✓ Ollama服务已正常运行，无需启动")
        logger.info("现在可以运行: python start_server.py")
        return 0
    
    # 2. 检查端口占用
    logger.info("\n2. 检查端口占用...")
    pid, proc_name = check_port_usage()
    
    if pid:
        if proc_name and 'ollama' in proc_name.lower():
            logger.info("发现僵死的Ollama进程，尝试终止...")
            if kill_process(pid):
                time.sleep(2)
                logger.info("已清理僵死进程")
            else:
                logger.error("无法终止进程，请手动处理或重启电脑")
                return 1
        else:
            logger.warning(f"端口被其他程序占用: {proc_name}")
            logger.info("请手动处理端口冲突")
            return 1
    
    # 3. 查找Ollama
    logger.info("\n3. 查找Ollama安装...")
    ollama_path = find_ollama()
    if not ollama_path:
        logger.error("未找到Ollama，请先安装Ollama")
        return 1
    
    # 4. 启动服务
    logger.info("\n4. 启动Ollama服务...")
    if start_ollama_service(ollama_path):
        logger.info("\n" + "=" * 50)
        logger.info("✓ Ollama服务启动成功!")
        logger.info("现在可以运行: python start_server.py")
        logger.info("=" * 50)
        return 0
    else:
        logger.error("\n" + "=" * 50)
        logger.error("✗ Ollama服务启动失败")
        logger.error("请手动启动: " + ollama_path + " serve")
        logger.error("=" * 50)
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)
