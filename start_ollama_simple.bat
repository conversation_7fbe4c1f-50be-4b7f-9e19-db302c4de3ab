@echo off
chcp 65001 >nul
echo ========================================
echo 启动Ollama服务
echo ========================================
echo.

if not exist "D:\ollama\ollama.exe" (
    echo ✗ 找不到Ollama: D:\ollama\ollama.exe
    echo 请检查Ollama安装路径
    pause
    exit /b 1
)

echo ✓ 找到Ollama: D:\ollama\ollama.exe
echo.

echo 检查可用模型...
"D:\ollama\ollama.exe" list
echo.

echo 启动Ollama服务...
echo.
echo 重要提示:
echo 1. 请保持此窗口打开
echo 2. 不要关闭此窗口
echo 3. 在另一个窗口运行 simple_start.bat
echo.
echo 按Ctrl+C停止服务
echo.

"D:\ollama\ollama.exe" serve
