#!/usr/bin/env python3
"""
启动脚本 - 用于启动MCP服务器
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import ensure_directories, LOG_LEVEL, LOG_FORMAT
from mcp_server import main

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=LOG_LEVEL,
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('mcp_server.log', encoding='utf-8')
        ]
    )

def check_prerequisites():
    """检查前置条件"""
    logger = logging.getLogger(__name__)
    
    # 检查必要的目录
    try:
        ensure_directories()
        logger.info("目录检查完成")
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False
    
    # 检查Ollama是否可访问
    try:
        import ollama
        client = ollama.Client()
        models = client.list()
        logger.info(f"Ollama连接成功，可用模型数量: {len(models['models'])}")
    except Exception as e:
        logger.error(f"Ollama连接失败: {e}")
        logger.error("请确保Ollama服务正在运行")
        return False
    
    return True

async def main_wrapper():
    """主函数包装器"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=" * 50)
        logger.info("MCP Qdrant文档向量化工具启动")
        logger.info("=" * 50)
        
        # 检查前置条件
        if not check_prerequisites():
            logger.error("前置条件检查失败，程序退出")
            return 1
        
        # 启动主程序
        await main()
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.error(f"程序运行失败: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 运行程序
    exit_code = asyncio.run(main_wrapper())
    sys.exit(exit_code)
