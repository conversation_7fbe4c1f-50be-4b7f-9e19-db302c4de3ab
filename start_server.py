#!/usr/bin/env python3
"""
启动脚本 - 用于启动MCP服务器
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import ensure_directories, LOG_LEVEL, LOG_FORMAT
from mcp_server import main

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=LOG_LEVEL,
        format=LOG_FORMAT,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('mcp_server.log', encoding='utf-8')
        ]
    )

def check_prerequisites():
    """检查前置条件"""
    logger = logging.getLogger(__name__)
    
    # 检查必要的目录
    try:
        ensure_directories()
        logger.info("目录检查完成")
    except Exception as e:
        logger.error(f"创建目录失败: {e}")
        return False
    
    # 检查Ollama是否可访问
    try:
        import ollama
        import requests

        # 先检查HTTP连接
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            logger.error(f"Ollama HTTP服务响应异常: {response.status_code}")
            return False

        # 再检查ollama客户端
        client = ollama.Client()
        models = client.list()
        logger.info(f"Ollama连接成功，可用模型数量: {len(models['models'])}")

        # 检查嵌入模型
        from config import EMBEDDING_MODEL
        model_names = [model['name'] for model in models['models']]
        if EMBEDDING_MODEL in model_names:
            logger.info(f"✓ 嵌入模型可用: {EMBEDDING_MODEL}")
        else:
            logger.warning(f"⚠ 嵌入模型未找到: {EMBEDDING_MODEL}")
            logger.info(f"可用模型: {model_names}")

    except requests.exceptions.ConnectionError:
        logger.error("无法连接到Ollama服务 (端口11434)")
        logger.error("请检查:")
        logger.error("1. Ollama服务是否运行: D:\\ollama\\ollama.exe serve")
        logger.error("2. 端口11434是否被占用")
        logger.error("3. 运行修复脚本: python fix_port_issue.py")
        return False
    except Exception as e:
        logger.error(f"Ollama连接失败: {e}")
        logger.error("请确保Ollama服务正在运行")
        logger.error("如果端口被占用，请运行: python fix_port_issue.py")
        return False
    
    return True

async def main_wrapper():
    """主函数包装器"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=" * 50)
        logger.info("MCP Qdrant文档向量化工具启动")
        logger.info("=" * 50)
        
        # 检查前置条件
        if not check_prerequisites():
            logger.error("前置条件检查失败，程序退出")
            return 1
        
        # 启动主程序
        await main()
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.error(f"程序运行失败: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    # 设置日志
    setup_logging()
    
    # 运行程序
    exit_code = asyncio.run(main_wrapper())
    sys.exit(exit_code)
