#!/usr/bin/env python3
"""
系统测试脚本
"""
import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import ensure_directories
from embedding_service import embedding_service
from vector_store import vector_store
from document_processor import document_processor
from file_monitor import file_monitor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_embedding_service():
    """测试嵌入服务"""
    logger.info("测试嵌入服务...")
    
    try:
        # 检查模型可用性
        available = await embedding_service.check_model_availability()
        logger.info(f"模型可用性: {available}")
        
        if not available:
            logger.info("正在拉取模型...")
            success = await embedding_service.pull_model_if_needed()
            if not success:
                logger.error("拉取模型失败")
                return False
        
        # 测试嵌入
        test_text = "这是一个测试文档，用于验证嵌入服务是否正常工作。"
        embedding = await embedding_service.get_embedding(test_text)
        
        if embedding:
            logger.info(f"嵌入向量维度: {len(embedding)}")
            logger.info("嵌入服务测试通过 ✓")
            return True
        else:
            logger.error("获取嵌入向量失败")
            return False
            
    except Exception as e:
        logger.error(f"嵌入服务测试失败: {e}")
        return False

async def test_vector_store():
    """测试向量存储"""
    logger.info("测试向量存储...")
    
    try:
        # 初始化
        success = await vector_store.initialize()
        if not success:
            logger.error("向量存储初始化失败")
            return False
        
        # 获取集合信息
        info = await vector_store.get_collection_info()
        if info:
            logger.info(f"集合信息: {info}")
        
        logger.info("向量存储测试通过 ✓")
        return True
        
    except Exception as e:
        logger.error(f"向量存储测试失败: {e}")
        return False

async def test_document_processor():
    """测试文档处理器"""
    logger.info("测试文档处理器...")
    
    try:
        # 创建测试文件
        test_file = "test_document.txt"
        test_content = "这是一个测试文档。\n包含多行文本内容。\n用于测试文档处理功能。"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 处理文档
        result = await document_processor.process_file(test_file)
        
        if result:
            logger.info(f"文档处理成功: {result['file_name']}")
            logger.info(f"内容长度: {len(result['content'])}")
            logger.info("文档处理器测试通过 ✓")
            
            # 清理测试文件
            os.remove(test_file)
            return True
        else:
            logger.error("文档处理失败")
            return False
            
    except Exception as e:
        logger.error(f"文档处理器测试失败: {e}")
        return False

async def test_integration():
    """集成测试"""
    logger.info("进行集成测试...")
    
    try:
        # 创建测试文档
        test_file = "integration_test.txt"
        test_content = "这是集成测试文档。包含了用于测试整个流程的内容。"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 处理文档
        doc_data = await document_processor.process_file(test_file)
        if not doc_data:
            logger.error("文档处理失败")
            return False
        
        # 获取嵌入向量
        embedding = await embedding_service.get_embedding(doc_data['content'])
        if not embedding:
            logger.error("获取嵌入向量失败")
            return False
        
        # 添加到向量数据库
        success = await vector_store.add_document(doc_data, embedding)
        if not success:
            logger.error("添加到向量数据库失败")
            return False
        
        # 搜索测试
        search_embedding = await embedding_service.get_embedding("测试文档")
        if not search_embedding:
            logger.error("获取搜索嵌入向量失败")
            return False
        
        results = await vector_store.search_similar(search_embedding, limit=1)
        if not results:
            logger.error("搜索失败")
            return False
        
        logger.info(f"搜索结果: {results[0]['payload']['file_name']}")
        logger.info(f"相似度: {results[0]['score']:.4f}")
        
        # 清理
        await vector_store.delete_document(test_file)
        os.remove(test_file)
        
        logger.info("集成测试通过 ✓")
        return True
        
    except Exception as e:
        logger.error(f"集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("MCP Qdrant工具系统测试")
    logger.info("=" * 50)
    
    # 确保目录存在
    ensure_directories()
    
    tests = [
        ("嵌入服务", test_embedding_service),
        ("向量存储", test_vector_store),
        ("文档处理器", test_document_processor),
        ("集成测试", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n开始测试: {test_name}")
        try:
            if await test_func():
                passed += 1
            else:
                logger.error(f"{test_name} 测试失败")
        except Exception as e:
            logger.error(f"{test_name} 测试异常: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("所有测试通过! 系统准备就绪 🎉")
        return 0
    else:
        logger.error("部分测试失败，请检查配置和依赖")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
