"""
Qdrant向量数据库操作
"""
import logging
import uuid
from typing import List, Optional, Dict, Any
from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct, 
    Filter, FieldCondition, MatchValue, SearchRequest
)
from config import (
    QDRANT_HOST, QDRANT_PORT, QDRANT_PATH, 
    QDRANT_COLLECTION_NAME, VECTOR_DIMENSION
)

logger = logging.getLogger(__name__)

class VectorStore:
    """Qdrant向量数据库操作类"""
    
    def __init__(self):
        # 使用本地文件存储
        self.client = QdrantClient(path=QDRANT_PATH)
        self.collection_name = QDRANT_COLLECTION_NAME
        
    async def initialize(self) -> bool:
        """初始化向量数据库"""
        try:
            # 检查集合是否存在
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # 创建集合
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=VECTOR_DIMENSION,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"创建集合: {self.collection_name}")
            else:
                logger.info(f"集合已存在: {self.collection_name}")
            
            return True
            
        except Exception as e:
            logger.error(f"初始化向量数据库失败: {e}")
            return False
    
    async def add_document(self, doc_data: Dict[str, Any], embedding: List[float]) -> bool:
        """添加文档到向量数据库"""
        try:
            point_id = str(uuid.uuid4())
            
            point = PointStruct(
                id=point_id,
                vector=embedding,
                payload={
                    'file_path': doc_data['file_path'],
                    'file_name': doc_data['file_name'],
                    'file_ext': doc_data['file_ext'],
                    'content': doc_data['content'][:1000],  # 限制内容长度
                    'full_content': doc_data['content'],
                    'size': doc_data['size'],
                    'modified_time': doc_data['modified_time'],
                    'created_time': doc_data['created_time'],
                }
            )
            
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.info(f"添加文档成功: {doc_data['file_name']}")
            return True
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False
    
    async def search_similar(self, query_embedding: List[float], limit: int = 5) -> List[Dict[str, Any]]:
        """搜索相似文档"""
        try:
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                with_payload=True
            )
            
            results = []
            for hit in search_result:
                results.append({
                    'id': hit.id,
                    'score': hit.score,
                    'payload': hit.payload
                })
            
            return results
            
        except Exception as e:
            logger.error(f"搜索相似文档失败: {e}")
            return []
    
    async def delete_document(self, file_path: str) -> bool:
        """根据文件路径删除文档"""
        try:
            # 先搜索要删除的文档
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="file_path",
                        match=MatchValue(value=file_path)
                    )
                ]
            )
            
            search_result = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=filter_condition,
                with_payload=True
            )
            
            if search_result[0]:  # 如果找到文档
                point_ids = [point.id for point in search_result[0]]
                self.client.delete(
                    collection_name=self.collection_name,
                    points_selector=point_ids
                )
                logger.info(f"删除文档成功: {file_path}")
                return True
            else:
                logger.warning(f"未找到要删除的文档: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    async def update_document(self, doc_data: Dict[str, Any], embedding: List[float]) -> bool:
        """更新文档"""
        try:
            # 先删除旧文档
            await self.delete_document(doc_data['file_path'])
            # 再添加新文档
            return await self.add_document(doc_data, embedding)
            
        except Exception as e:
            logger.error(f"更新文档失败: {e}")
            return False
    
    async def get_all_documents(self) -> List[Dict[str, Any]]:
        """获取所有文档"""
        try:
            scroll_result = self.client.scroll(
                collection_name=self.collection_name,
                with_payload=True,
                limit=1000
            )
            
            documents = []
            for point in scroll_result[0]:
                documents.append({
                    'id': point.id,
                    'payload': point.payload
                })
            
            return documents
            
        except Exception as e:
            logger.error(f"获取所有文档失败: {e}")
            return []
    
    async def get_collection_info(self) -> Optional[Dict[str, Any]]:
        """获取集合信息"""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                'name': info.config.params.vectors.size,
                'vectors_count': info.vectors_count,
                'points_count': info.points_count,
                'status': info.status
            }
        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return None

# 全局向量存储实例
vector_store = VectorStore()

async def main():
    """测试向量存储"""
    print("测试向量存储...")
    
    # 初始化
    success = await vector_store.initialize()
    print(f"初始化结果: {success}")
    
    if success:
        # 获取集合信息
        info = await vector_store.get_collection_info()
        if info:
            print(f"集合信息: {info}")
        
        # 获取所有文档
        docs = await vector_store.get_all_documents()
        print(f"文档数量: {len(docs)}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
