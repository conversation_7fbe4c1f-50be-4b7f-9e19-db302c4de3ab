# MCP Qdrant工具启动说明

## 🚀 快速启动

### 最简单的方式（推荐）

```bash
python auto_start.py
```

这个脚本会自动完成所有设置和启动步骤。

### 如果遇到问题

1. **先启动Ollama服务**：
   ```bash
   python start_ollama.py
   ```

2. **再启动MCP服务器**：
   ```bash
   python start_server.py
   ```

## 📁 新的Python启动脚本

| 脚本名称 | 功能描述 |
|---------|---------|
| `auto_start.py` | 全自动启动，包含所有检查和设置 |
| `quick_start.py` | 快速启动，适合已配置环境 |
| `start_ollama.py` | 启动和管理Ollama服务 |
| `start_server.py` | 启动MCP服务器 |
| `test_system.py` | 系统测试 |
| `fix_ollama_connection.py` | 修复Ollama连接问题 |
| `fix_port_issue.py` | 修复端口占用问题 |

## 🔧 故障排除

### 如果Python命令不工作

确保使用正确的Python命令：
- `python auto_start.py`
- `py auto_start.py`
- `python3 auto_start.py`
- 或使用完整路径：`C:\Python311\python.exe auto_start.py`

### 如果Ollama连接失败

1. 检查Ollama是否安装在 `D:\ollama\ollama.exe`
2. 运行 `python start_ollama.py` 启动服务
3. 检查端口11434是否被占用

### 如果依赖包缺失

```bash
python -m pip install -r requirements.txt
```

## 🎯 成功标志

当看到以下信息时，说明启动成功：

```
INFO:__main__:启动MCP服务器: qdrant-document-server v1.0.0
INFO:__main__:初始化向量数据库...
INFO:__main__:检查嵌入模型...
INFO:__main__:启动文件监控...
INFO:__main__:启动MCP服务器...
```

## 📞 需要帮助？

如果仍有问题，请运行诊断脚本：
```bash
python fix_ollama_connection.py
```

这会提供详细的诊断信息。

---

**重要提醒：所有.bat文件已删除，现在只使用Python脚本启动！**
