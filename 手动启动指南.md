# 手动启动指南

## 🚨 当前问题

根据你的截图，系统无法识别Python命令。这是因为Python没有正确安装或没有添加到系统PATH。

## 🔍 第一步：诊断问题

运行诊断脚本：
```
双击运行: quick_test.bat
```

这个脚本会检查：
- Python是否安装
- Ollama是否安装
- 项目文件是否完整

## 🛠️ 解决方案

### 方案1：使用诊断脚本的建议

运行`quick_test.bat`后，它会给出具体的启动命令。

### 方案2：手动查找Python

1. **检查Python是否安装**：
   - 按`Win + R`，输入`cmd`，回车
   - 尝试输入以下命令：
     ```
     py --version
     python --version
     python3 --version
     ```

2. **查找Python安装位置**：
   常见位置：
   ```
   C:\Python39\python.exe
   C:\Python310\python.exe
   C:\Python311\python.exe
   C:\Python312\python.exe
   C:\Python313\python.exe
   C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe
   ```

3. **测试Python路径**：
   找到Python后，测试是否工作：
   ```
   "C:\Python311\python.exe" --version
   ```

### 方案3：重新安装Python

如果找不到Python：

1. **下载Python**：
   - 访问 https://python.org
   - 下载Python 3.8或更高版本

2. **安装时注意**：
   - ✅ 勾选 "Add Python to PATH"
   - ✅ 选择 "Install for all users"

3. **验证安装**：
   ```
   python --version
   py --version
   ```

## 🚀 手动启动步骤

假设你找到了Python路径（例如：`C:\Python311\python.exe`）：

### 步骤1：启动Ollama服务

打开命令行，运行：
```cmd
D:\ollama\ollama.exe serve
```
**保持这个窗口打开！**

### 步骤2：安装依赖（新窗口）

```cmd
cd /d D:\work\project2
C:\Python311\python.exe -m pip install -r requirements.txt
```

### 步骤3：测试连接

```cmd
C:\Python311\python.exe fix_ollama_connection.py
```

### 步骤4：运行系统测试

```cmd
C:\Python311\python.exe test_system.py
```

### 步骤5：启动MCP服务器

```cmd
C:\Python311\python.exe start_server.py
```

## 📋 完整的命令行示例

假设Python在`C:\Python311\python.exe`：

```cmd
REM 窗口1 - 启动Ollama
D:\ollama\ollama.exe serve

REM 窗口2 - 启动MCP工具
cd /d D:\work\project2
C:\Python311\python.exe -m pip install -r requirements.txt
C:\Python311\python.exe fix_ollama_connection.py
C:\Python311\python.exe test_system.py
C:\Python311\python.exe start_server.py
```

## 🔧 创建自定义启动脚本

如果你找到了Python路径，可以创建自定义脚本：

1. **创建文件**：`my_start.bat`

2. **内容**（替换Python路径）：
```batch
@echo off
chcp 65001 >nul
echo 启动MCP工具...

set PYTHON_CMD=C:\Python311\python.exe
set OLLAMA_CMD=D:\ollama\ollama.exe

echo 检查Ollama服务...
"%OLLAMA_CMD%" list >nul 2>&1
if errorlevel 1 (
    echo 请先启动Ollama服务: %OLLAMA_CMD% serve
    pause
    exit /b 1
)

echo 安装依赖...
"%PYTHON_CMD%" -m pip install -r requirements.txt

echo 测试连接...
"%PYTHON_CMD%" fix_ollama_connection.py

echo 运行测试...
"%PYTHON_CMD%" test_system.py

echo 启动服务器...
"%PYTHON_CMD%" start_server.py

pause
```

## ❓ 常见问题

### Q: 找不到Python
**A:** 
1. 检查是否安装了Python
2. 重新安装Python，勾选"Add to PATH"
3. 使用完整路径运行

### Q: Ollama服务启动失败
**A:**
1. 检查路径：`D:\ollama\ollama.exe`
2. 以管理员身份运行
3. 检查防火墙设置

### Q: 依赖安装失败
**A:**
1. 检查网络连接
2. 使用管理员权限
3. 更新pip：`python -m pip install --upgrade pip`

## 📞 获取帮助

如果仍有问题，请提供：
1. `quick_test.bat` 的完整输出
2. Python安装方式
3. 具体的错误信息

## 🎯 成功标志

当看到以下信息时，说明启动成功：
```
INFO:__main__:启动MCP服务器: qdrant-document-server v1.0.0
INFO:__main__:初始化向量数据库...
INFO:__main__:检查嵌入模型...
INFO:__main__:启动文件监控...
INFO:__main__:启动MCP服务器...
```
