# 端口11434占用问题解决方案

## 🚨 问题描述

错误信息：
```
Error: listen tcp 127.0.0.1:11434: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
```

这表示端口11434已经被占用，通常是因为：
1. Ollama服务已经在运行
2. 有僵死的Ollama进程
3. 其他程序占用了该端口

## 🔍 快速诊断

### 方法1：使用自动修复脚本
```
双击运行: solve_port_problem.bat
```

### 方法2：使用Python诊断脚本
```
python fix_port_issue.py
```

### 方法3：手动检查
```cmd
netstat -ano | findstr :11434
```

## 🛠️ 解决方案

### 情况1：Ollama服务正常运行

如果Ollama已经在运行，这是**好事**！直接运行：
```
python start_server.py
```

### 情况2：僵死的Ollama进程

1. **查找占用进程**：
   ```cmd
   netstat -ano | findstr :11434
   ```

2. **终止进程**（替换PID为实际值）：
   ```cmd
   taskkill /PID 1234 /F
   ```

3. **重新启动Ollama**：
   ```cmd
   D:\ollama\ollama.exe serve
   ```

### 情况3：其他程序占用

1. **检查占用程序**：
   ```cmd
   netstat -ano | findstr :11434
   tasklist /FI "PID eq 占用的PID"
   ```

2. **如果是不重要的程序**，终止它
3. **如果是重要程序**，考虑更改Ollama端口

## 🚀 推荐的完整解决流程

### 步骤1：自动修复
```
双击运行: solve_port_problem.bat
```

### 步骤2：验证修复
```
curl http://localhost:11434/api/tags
```
或在浏览器访问：`http://localhost:11434/api/tags`

### 步骤3：启动MCP服务器
```
python start_server.py
```

## 🔧 高级解决方案

### 更改Ollama端口

如果端口冲突无法解决，可以更改Ollama端口：

1. **设置环境变量**：
   ```cmd
   set OLLAMA_HOST=0.0.0.0:11435
   ```

2. **启动Ollama**：
   ```cmd
   D:\ollama\ollama.exe serve
   ```

3. **修改项目配置**：
   编辑 `config.py`：
   ```python
   OLLAMA_HOST = "http://localhost:11435"
   ```

### 使用任务管理器

1. 按 `Ctrl + Shift + Esc` 打开任务管理器
2. 查找 `ollama.exe` 进程
3. 右键 → 结束任务
4. 重新启动Ollama服务

## 📋 验证步骤

### 1. 检查端口状态
```cmd
netstat -ano | findstr :11434
```

### 2. 测试Ollama服务
```cmd
curl http://localhost:11434/api/tags
```

### 3. 检查模型列表
```cmd
D:\ollama\ollama.exe list
```

### 4. 测试MCP连接
```cmd
python fix_ollama_connection.py
```

## ❓ 常见问题

### Q: 脚本提示"需要管理员权限"
**A:** 右键脚本 → "以管理员身份运行"

### Q: 终止进程后仍然占用
**A:** 等待几秒钟，或重启电脑

### Q: curl命令不存在
**A:** 在浏览器访问 `http://localhost:11434/api/tags`

### Q: 修复后MCP服务器仍然失败
**A:** 检查Python和依赖包是否正确安装

## 🎯 成功标志

当看到以下信息时，说明问题已解决：

1. **端口检查**：
   ```
   ✓ 端口11434未被占用 或 ✓ Ollama服务正常运行
   ```

2. **服务测试**：
   ```json
   {"models":[{"name":"mxbai-embed-large:latest",...}]}
   ```

3. **MCP启动**：
   ```
   INFO:__main__:Ollama连接成功，可用模型数量: 1
   INFO:__main__:✓ 嵌入模型可用: mxbai-embed-large:latest
   ```

## 🆘 最后手段

如果所有方法都失败：

1. **重启电脑** - 最简单有效
2. **重新安装Ollama**
3. **使用不同端口**
4. **检查防火墙设置**

记住：端口占用通常说明Ollama已经在运行，这是好事！
