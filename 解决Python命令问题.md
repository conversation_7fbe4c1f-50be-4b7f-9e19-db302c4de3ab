# 解决"不是内部或外部命令"问题

## 🔍 问题原因
你遇到的错误是因为Python没有添加到系统PATH环境变量中，所以命令行无法识别`python`命令。

## 🚀 快速解决方案

### 方案1: 使用简化启动脚本（推荐）

1. **启动Ollama服务**（保持窗口打开）：
   ```
   双击运行: start_ollama_simple.bat
   ```

2. **在新的命令行窗口启动MCP工具**：
   ```
   双击运行: simple_start.bat
   ```

### 方案2: 使用完整路径脚本

如果方案1不工作，运行：
```
双击运行: run_with_full_path.bat
```
这个脚本会自动查找Python安装位置。

### 方案3: 环境设置脚本

运行完整的环境设置：
```
双击运行: setup_environment.bat
```

## 🔧 手动解决方法

### 1. 查找Python安装位置

运行：
```
双击运行: find_python.bat
```

常见的Python安装位置：
- `C:\Python39\python.exe`
- `C:\Python310\python.exe`
- `C:\Python311\python.exe`
- `C:\Python312\python.exe`
- `C:\Python313\python.exe`
- `C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe`

### 2. 使用完整路径运行

找到Python路径后，使用完整路径运行命令：

```cmd
# 例如，如果Python在 C:\Python311\python.exe
C:\Python311\python.exe --version
C:\Python311\python.exe -m pip install -r requirements.txt
C:\Python311\python.exe test_system.py
```

### 3. 添加Python到PATH（永久解决）

1. 右键"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中找到"Path"，点击"编辑"
5. 点击"新建"，添加Python安装目录
6. 例如添加：`C:\Python311\` 和 `C:\Python311\Scripts\`
7. 点击"确定"保存
8. 重新打开命令行窗口

## 📋 完整启动流程

### 第一步：启动Ollama服务
```
双击运行: start_ollama_simple.bat
```
**重要：保持这个窗口打开！**

### 第二步：启动MCP工具
在新的命令行窗口：
```
双击运行: simple_start.bat
```

或者手动运行：
```cmd
py --version
py -m pip install -r requirements.txt
py fix_ollama_connection.py
py test_system.py
py start_server.py
```

## 🧪 验证步骤

1. **检查Python**：
   ```cmd
   py --version
   ```
   应该显示Python版本信息

2. **检查Ollama**：
   ```cmd
   D:\ollama\ollama.exe list
   ```
   应该显示模型列表

3. **检查连接**：
   ```cmd
   py fix_ollama_connection.py
   ```
   应该显示所有检查通过

## ❓ 常见问题

### Q: 运行simple_start.bat还是提示找不到py命令
**A:** 运行 `run_with_full_path.bat`，它会自动查找Python

### Q: Ollama服务启动失败
**A:** 检查路径 `D:\ollama\ollama.exe` 是否存在

### Q: 依赖安装失败
**A:** 尝试使用管理员权限运行命令行

### Q: 端口被占用
**A:** 重启电脑或更改端口配置

## 📞 需要帮助？

如果以上方法都不工作，请提供：
1. 运行 `find_python.bat` 的输出
2. Python安装方式（从官网下载、Microsoft Store等）
3. Windows版本信息

## 🎯 成功标志

当看到以下信息时，说明启动成功：
```
INFO:__main__:启动MCP服务器: qdrant-document-server v1.0.0
INFO:__main__:初始化向量数据库...
INFO:__main__:检查嵌入模型...
INFO:__main__:启动文件监控...
INFO:__main__:启动MCP服务器...
```

记住：**Ollama服务必须先启动并保持运行！**
